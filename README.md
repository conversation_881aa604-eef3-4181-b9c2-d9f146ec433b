# Serenity BDD Automation Framework

A comprehensive automation test framework built with **Serenity BDD**, **Java**, **Selenium WebDriver**, and **Maven**. This framework supports both **UI automation** and **API testing** with **JUnit** and **Cucumber** integration.

## 🚀 Features

- **Serenity BDD** for rich test reporting and BDD support
- **Selenium WebDriver** for UI automation with Page Object Model (POM)
- **REST Assured** integration for API testing
- **JUnit 5** and **Cucumber** support for flexible test execution
- **Maven** for dependency management and build automation
- **WebDriverManager** for automatic driver management
- **Parallel execution** support
- **Environment-specific configurations**
- **Comprehensive logging** with Logback
- **Screenshot capture** on test failures
- **Test data management** utilities

## 📁 Project Structure

```
serenity-automation-framework/
├── src/
│   ├── main/java/com/automation/framework/
│   │   ├── api/                    # API client classes
│   │   │   ├── BaseApiClient.java
│   │   │   └── UserApiClient.java
│   │   ├── config/                 # Configuration classes
│   │   ├── pages/                  # Page Object Model classes
│   │   │   ├── BasePage.java
│   │   │   ├── LoginPage.java
│   │   │   └── ProductsPage.java
│   │   ├── steps/                  # Step definition classes
│   │   │   ├── ApiSteps.java
│   │   │   ├── LoginSteps.java
│   │   │   └── ProductSteps.java
│   │   └── utils/                  # Utility classes
│   │       ├── ConfigurationManager.java
│   │       ├── ScreenshotUtils.java
│   │       └── TestDataManager.java
│   └── test/
│       ├── java/
│       │   ├── com/automation/framework/
│       │   │   ├── ApiTest.java           # JUnit API tests
│       │   │   ├── LoginTest.java         # JUnit UI tests
│       │   │   ├── ProductTest.java       # JUnit UI tests
│       │   │   └── stepdefinitions/       # Cucumber step definitions
│       │   │       ├── LoginStepDefinitions.java
│       │   │       └── ProductStepDefinitions.java
│       │   └── runners/                   # Test runners
│       │       ├── CucumberTestRunner.java
│       │       ├── RegressionTestRunner.java
│       │       └── SmokeTestRunner.java
│       └── resources/
│           ├── features/                  # Cucumber feature files
│           │   ├── login.feature
│           │   └── products.feature
│           ├── config/                    # Configuration files
│           │   └── environments.properties
│           ├── testdata/                  # Test data files
│           │   ├── api.properties
│           │   ├── products.properties
│           │   └── users.properties
│           ├── logback-test.xml          # Logging configuration
│           └── serenity.properties       # Serenity configuration
├── target/                               # Build output directory
│   ├── site/serenity/                   # Serenity reports
│   ├── cucumber-reports/                # Cucumber reports
│   ├── screenshots/                     # Test screenshots
│   └── logs/                           # Application logs
├── pom.xml                              # Maven configuration
└── README.md                           # This file
```

## 🛠️ Technologies Used

| Technology | Version | Purpose |
|------------|---------|---------|
| Java | 11+ | Programming language |
| Maven | 3.6+ | Build and dependency management |
| Serenity BDD | 4.0.1 | BDD framework and reporting |
| Selenium WebDriver | 4.15.0 | UI automation |
| JUnit 5 | 5.10.0 | Unit testing framework |
| Cucumber | 7.14.0 | BDD testing framework |
| REST Assured | 5.3.2 | API testing |
| WebDriverManager | 5.5.3 | Automatic driver management |
| AssertJ | 3.24.2 | Fluent assertions |
| Logback | 1.4.11 | Logging framework |

## 🚦 Prerequisites

- **Java 11** or higher
- **Maven 3.6** or higher
- **Chrome browser** (default) or other supported browsers
- **Internet connection** for downloading dependencies and WebDriver

## ⚡ Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd serenity-automation-framework
```

### 2. Install Dependencies
```bash
mvn clean install
```

### 3. Run Tests

#### Run All Tests
```bash
mvn clean verify
```

#### Run JUnit Tests Only
```bash
mvn clean verify -Pjunit
```

#### Run Cucumber Tests Only
```bash
mvn clean verify -Pcucumber
```

#### Run Smoke Tests
```bash
mvn clean verify -Dtest=SmokeTestRunner
```

#### Run Specific Test Class
```bash
mvn clean verify -Dtest=LoginTest
```

#### Run Tests with Specific Tags
```bash
mvn clean verify -Dcucumber.filter.tags="@smoke"
```

### 4. Generate Reports
```bash
mvn serenity:aggregate
```

Reports will be generated in `target/site/serenity/index.html`

## 🔧 Configuration

### Environment Configuration

Configure different environments in `src/test/resources/config/environments.properties`:

```properties
# Local Environment
local.base.url=https://www.saucedemo.com
local.api.url=https://reqres.in/api
local.browser=chrome
local.headless=false

# Staging Environment  
staging.base.url=https://staging.saucedemo.com
staging.browser=chrome
staging.headless=true
```

### Serenity Configuration

Main configuration in `src/test/resources/serenity.properties`:

```properties
# WebDriver Configuration
webdriver.driver=chrome
webdriver.base.url=https://www.saucedemo.com
headless.mode=false

# Screenshot Configuration
serenity.take.screenshots=FOR_FAILURES
serenity.capture.screenshot.on.failure=true

# Reporting Configuration
serenity.project.name=Automation Framework
serenity.outputDirectory=target/site/serenity
```

## 🧪 Running Tests

### Command Line Options

| Command | Description |
|---------|-------------|
| `mvn clean verify` | Run all tests |
| `mvn clean verify -Pjunit` | Run JUnit tests only |
| `mvn clean verify -Pcucumber` | Run Cucumber tests only |
| `mvn clean verify -Dwebdriver.driver=firefox` | Run with Firefox |
| `mvn clean verify -Dheadless.mode=true` | Run in headless mode |
| `mvn clean verify -Denvironment=staging` | Run against staging environment |
| `mvn clean verify -Dcucumber.filter.tags="@smoke"` | Run smoke tests only |
| `mvn clean verify -Dcucumber.filter.tags="@login and @positive"` | Run specific tagged tests |

### Parallel Execution

Enable parallel execution by configuring `pom.xml`:

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-failsafe-plugin</artifactId>
    <configuration>
        <parallel>classes</parallel>
        <threadCount>2</threadCount>
        <forkCount>2</forkCount>
    </configuration>
</plugin>
```

## 📊 Reports

### Serenity Reports
- **Location**: `target/site/serenity/index.html`
- **Features**: Rich HTML reports with screenshots, step details, and test results
- **Generation**: Automatic after test execution

### Cucumber Reports
- **Location**: `target/cucumber-reports/`
- **Formats**: HTML, JSON, XML
- **Features**: Standard Cucumber reporting

### Screenshots
- **Location**: `target/screenshots/`
- **Trigger**: Automatic on test failures
- **Format**: PNG with timestamp

## 🔍 Test Examples

### JUnit Test Example
```java
@ExtendWith(SerenityJUnit5Extension.class)
@DisplayName("Login Tests")
public class LoginTest {
    
    @Steps
    LoginSteps loginSteps;
    
    @Test
    @DisplayName("Successful login with valid credentials")
    public void testSuccessfulLogin() {
        loginSteps.navigateToLoginPage();
        loginSteps.loginWithCredentials("standard_user", "secret_sauce");
        loginSteps.verifySuccessfulLogin();
    }
}
```

### Cucumber Feature Example
```gherkin
Feature: User Login
  As a user
  I want to be able to login to the application
  So that I can access the products page

  @smoke @positive
  Scenario: Successful login with valid credentials
    Given I am on the login page
    When I login with username "standard_user" and password "secret_sauce"
    Then I should be successfully logged in
```

### API Test Example
```java
@Test
@DisplayName("Get all users")
public void testGetAllUsers() {
    apiSteps.getAllUsers();
    apiSteps.verifyResponseStatusCode(200);
    apiSteps.verifyResponseContainsField("data");
    apiSteps.verifyResponseTime(5000);
}
```

## 🏗️ Extending the Framework

### Adding New Page Objects
1. Create a new page class extending `BasePage`
2. Add page elements using `@FindBy` annotations
3. Implement page-specific methods
4. Create corresponding step definitions

### Adding New API Clients
1. Create a new API client extending `BaseApiClient`
2. Define API endpoints and methods
3. Add corresponding step definitions
4. Create test scenarios

### Adding New Test Data
1. Add properties to existing files in `src/test/resources/testdata/`
2. Or create new properties files
3. Use `TestDataManager` to access test data

## 🐛 Troubleshooting

### Common Issues

1. **WebDriver Issues**
   - Ensure Chrome browser is installed
   - WebDriverManager will automatically download drivers
   - Check browser compatibility with Selenium version

2. **Test Failures**
   - Check screenshots in `target/screenshots/`
   - Review Serenity reports for detailed step information
   - Verify test data and environment configuration

3. **Build Issues**
   - Run `mvn clean install` to refresh dependencies
   - Check Java and Maven versions
   - Verify internet connectivity for dependency downloads

### Debug Mode
Run tests with debug logging:
```bash
mvn clean verify -Dserenity.logging=VERBOSE -Dserenity.log.level=DEBUG
```

## 📝 Best Practices

1. **Page Object Model**: Keep page elements and actions separate
2. **Step Definitions**: Make steps reusable and atomic
3. **Test Data**: Use external data files for test data
4. **Assertions**: Use meaningful assertion messages
5. **Screenshots**: Capture screenshots on failures for debugging
6. **Logging**: Use appropriate log levels for different information
7. **Tags**: Use Cucumber tags for test organization and execution

## 🤝 Contributing

1. Follow the existing code structure and naming conventions
2. Add appropriate logging and comments
3. Include both positive and negative test scenarios
4. Update documentation for new features
5. Ensure all tests pass before submitting changes

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
