@echo off
REM Serenity BDD Automation Framework Test Execution Script
REM This script provides various options for running tests

echo ========================================
echo Serenity BDD Automation Framework
echo ========================================
echo.

if "%1"=="" goto :show_help
if "%1"=="help" goto :show_help
if "%1"=="all" goto :run_all
if "%1"=="junit" goto :run_junit
if "%1"=="cucumber" goto :run_cucumber
if "%1"=="smoke" goto :run_smoke
if "%1"=="regression" goto :run_regression
if "%1"=="api" goto :run_api
if "%1"=="ui" goto :run_ui
if "%1"=="clean" goto :clean_reports
if "%1"=="reports" goto :generate_reports
goto :show_help

:show_help
echo Usage: run-tests.bat [option]
echo.
echo Options:
echo   all         - Run all tests (JUnit + Cucumber)
echo   junit       - Run JUnit tests only
echo   cucumber    - Run Cucumber tests only
echo   smoke       - Run smoke tests only
echo   regression  - Run regression tests
echo   api         - Run API tests only
echo   ui          - Run UI tests only
echo   clean       - Clean previous reports
echo   reports     - Generate Serenity reports only
echo   help        - Show this help message
echo.
echo Examples:
echo   run-tests.bat all
echo   run-tests.bat smoke
echo   run-tests.bat api
goto :end

:run_all
echo Running all tests...
call mvn clean verify
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ All tests completed successfully!
    call :generate_reports
) else (
    echo.
    echo ✗ Some tests failed. Check the reports for details.
)
goto :end

:run_junit
echo Running JUnit tests only...
call mvn clean verify -Pjunit
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ JUnit tests completed successfully!
    call :generate_reports
) else (
    echo.
    echo ✗ Some JUnit tests failed. Check the reports for details.
)
goto :end

:run_cucumber
echo Running Cucumber tests only...
call mvn clean verify -Pcucumber
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Cucumber tests completed successfully!
    call :generate_reports
) else (
    echo.
    echo ✗ Some Cucumber tests failed. Check the reports for details.
)
goto :end

:run_smoke
echo Running smoke tests...
call mvn clean verify -Dcucumber.filter.tags="@smoke"
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Smoke tests completed successfully!
    call :generate_reports
) else (
    echo.
    echo ✗ Some smoke tests failed. Check the reports for details.
)
goto :end

:run_regression
echo Running regression tests...
call mvn clean verify -Dtest=RegressionTestRunner
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Regression tests completed successfully!
    call :generate_reports
) else (
    echo.
    echo ✗ Some regression tests failed. Check the reports for details.
)
goto :end

:run_api
echo Running API tests only...
call mvn clean verify -Dcucumber.filter.tags="@api" -Dtest=ApiTest
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ API tests completed successfully!
    call :generate_reports
) else (
    echo.
    echo ✗ Some API tests failed. Check the reports for details.
)
goto :end

:run_ui
echo Running UI tests only...
call mvn clean verify -Dcucumber.filter.tags="@login or @products" -Dtest=LoginTest,ProductTest
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ UI tests completed successfully!
    call :generate_reports
) else (
    echo.
    echo ✗ Some UI tests failed. Check the reports for details.
)
goto :end

:clean_reports
echo Cleaning previous reports...
if exist target\site\serenity rmdir /s /q target\site\serenity
if exist target\cucumber-reports rmdir /s /q target\cucumber-reports
if exist target\screenshots rmdir /s /q target\screenshots
echo ✓ Reports cleaned successfully!
goto :end

:generate_reports
echo.
echo Generating Serenity reports...
call mvn serenity:aggregate
if %ERRORLEVEL% EQU 0 (
    echo ✓ Reports generated successfully!
    echo.
    echo 📊 View reports at:
    echo    Serenity: target\site\serenity\index.html
    echo    Cucumber: target\cucumber-reports\index.html
    echo.
) else (
    echo ✗ Failed to generate reports.
)
goto :end

:end
echo.
echo ========================================
pause
