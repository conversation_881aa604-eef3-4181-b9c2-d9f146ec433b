#!/bin/bash

# Serenity BDD Automation Framework Test Execution Script
# This script provides various options for running tests

echo "========================================"
echo "Serenity BDD Automation Framework"
echo "========================================"
echo

show_help() {
    echo "Usage: ./run-tests.sh [option]"
    echo
    echo "Options:"
    echo "  all         - Run all tests (JUnit + Cucumber)"
    echo "  junit       - Run JUnit tests only"
    echo "  cucumber    - Run Cucumber tests only"
    echo "  smoke       - Run smoke tests only"
    echo "  regression  - Run regression tests"
    echo "  api         - Run API tests only"
    echo "  ui          - Run UI tests only"
    echo "  clean       - Clean previous reports"
    echo "  reports     - Generate Serenity reports only"
    echo "  help        - Show this help message"
    echo
    echo "Examples:"
    echo "  ./run-tests.sh all"
    echo "  ./run-tests.sh smoke"
    echo "  ./run-tests.sh api"
    echo
}

generate_reports() {
    echo
    echo "Generating Serenity reports..."
    mvn serenity:aggregate
    if [ $? -eq 0 ]; then
        echo "✓ Reports generated successfully!"
        echo
        echo "📊 View reports at:"
        echo "   Serenity: target/site/serenity/index.html"
        echo "   Cucumber: target/cucumber-reports/index.html"
        echo
    else
        echo "✗ Failed to generate reports."
    fi
}

run_all() {
    echo "Running all tests..."
    mvn clean verify
    if [ $? -eq 0 ]; then
        echo
        echo "✓ All tests completed successfully!"
        generate_reports
    else
        echo
        echo "✗ Some tests failed. Check the reports for details."
    fi
}

run_junit() {
    echo "Running JUnit tests only..."
    mvn clean verify -Pjunit
    if [ $? -eq 0 ]; then
        echo
        echo "✓ JUnit tests completed successfully!"
        generate_reports
    else
        echo
        echo "✗ Some JUnit tests failed. Check the reports for details."
    fi
}

run_cucumber() {
    echo "Running Cucumber tests only..."
    mvn clean verify -Pcucumber
    if [ $? -eq 0 ]; then
        echo
        echo "✓ Cucumber tests completed successfully!"
        generate_reports
    else
        echo
        echo "✗ Some Cucumber tests failed. Check the reports for details."
    fi
}

run_smoke() {
    echo "Running smoke tests..."
    mvn clean verify -Dcucumber.filter.tags="@smoke"
    if [ $? -eq 0 ]; then
        echo
        echo "✓ Smoke tests completed successfully!"
        generate_reports
    else
        echo
        echo "✗ Some smoke tests failed. Check the reports for details."
    fi
}

run_regression() {
    echo "Running regression tests..."
    mvn clean verify -Dtest=RegressionTestRunner
    if [ $? -eq 0 ]; then
        echo
        echo "✓ Regression tests completed successfully!"
        generate_reports
    else
        echo
        echo "✗ Some regression tests failed. Check the reports for details."
    fi
}

run_api() {
    echo "Running API tests only..."
    mvn clean verify -Dcucumber.filter.tags="@api" -Dtest=ApiTest
    if [ $? -eq 0 ]; then
        echo
        echo "✓ API tests completed successfully!"
        generate_reports
    else
        echo
        echo "✗ Some API tests failed. Check the reports for details."
    fi
}

run_ui() {
    echo "Running UI tests only..."
    mvn clean verify -Dcucumber.filter.tags="@login or @products" -Dtest=LoginTest,ProductTest
    if [ $? -eq 0 ]; then
        echo
        echo "✓ UI tests completed successfully!"
        generate_reports
    else
        echo
        echo "✗ Some UI tests failed. Check the reports for details."
    fi
}

clean_reports() {
    echo "Cleaning previous reports..."
    rm -rf target/site/serenity
    rm -rf target/cucumber-reports
    rm -rf target/screenshots
    echo "✓ Reports cleaned successfully!"
}

# Main script logic
case "$1" in
    "all")
        run_all
        ;;
    "junit")
        run_junit
        ;;
    "cucumber")
        run_cucumber
        ;;
    "smoke")
        run_smoke
        ;;
    "regression")
        run_regression
        ;;
    "api")
        run_api
        ;;
    "ui")
        run_ui
        ;;
    "clean")
        clean_reports
        ;;
    "reports")
        generate_reports
        ;;
    "help"|"")
        show_help
        ;;
    *)
        echo "Unknown option: $1"
        echo
        show_help
        ;;
esac

echo
echo "========================================"
