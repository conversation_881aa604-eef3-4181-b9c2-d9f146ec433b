package com.automation.framework.api;

import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import net.serenitybdd.rest.SerenityRest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Base API Client class containing common REST API operations
 * Uses Serenity REST Assured for enhanced reporting
 */
public class BaseApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseApiClient.class);
    
    protected String baseUrl;
    protected RequestSpecification requestSpec;
    
    public BaseApiClient(String baseUrl) {
        this.baseUrl = baseUrl;
        setupRequestSpecification();
    }
    
    /**
     * Setup default request specification
     */
    private void setupRequestSpecification() {
        requestSpec = SerenityRest.given()
                .baseUri(baseUrl)
                .contentType(ContentType.JSON)
                .accept(ContentType.JSON)
                .log().all();
        
        logger.debug("Request specification setup with base URL: {}", baseUrl);
    }
    
    /**
     * Perform GET request
     * @param endpoint - API endpoint
     * @return Response
     */
    public Response get(String endpoint) {
        logger.info("Performing GET request to: {}", endpoint);
        Response response = requestSpec.when().get(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Perform GET request with path parameters
     * @param endpoint - API endpoint
     * @param pathParams - path parameters
     * @return Response
     */
    public Response get(String endpoint, Map<String, Object> pathParams) {
        logger.info("Performing GET request to: {} with path params: {}", endpoint, pathParams);
        Response response = requestSpec.pathParams(pathParams).when().get(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Perform GET request with query parameters
     * @param endpoint - API endpoint
     * @param queryParams - query parameters
     * @return Response
     */
    public Response getWithQueryParams(String endpoint, Map<String, Object> queryParams) {
        logger.info("Performing GET request to: {} with query params: {}", endpoint, queryParams);
        Response response = requestSpec.queryParams(queryParams).when().get(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Perform POST request
     * @param endpoint - API endpoint
     * @param body - request body
     * @return Response
     */
    public Response post(String endpoint, Object body) {
        logger.info("Performing POST request to: {} with body: {}", endpoint, body);
        Response response = requestSpec.body(body).when().post(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Perform POST request without body
     * @param endpoint - API endpoint
     * @return Response
     */
    public Response post(String endpoint) {
        logger.info("Performing POST request to: {}", endpoint);
        Response response = requestSpec.when().post(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Perform PUT request
     * @param endpoint - API endpoint
     * @param body - request body
     * @return Response
     */
    public Response put(String endpoint, Object body) {
        logger.info("Performing PUT request to: {} with body: {}", endpoint, body);
        Response response = requestSpec.body(body).when().put(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Perform PATCH request
     * @param endpoint - API endpoint
     * @param body - request body
     * @return Response
     */
    public Response patch(String endpoint, Object body) {
        logger.info("Performing PATCH request to: {} with body: {}", endpoint, body);
        Response response = requestSpec.body(body).when().patch(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Perform DELETE request
     * @param endpoint - API endpoint
     * @return Response
     */
    public Response delete(String endpoint) {
        logger.info("Performing DELETE request to: {}", endpoint);
        Response response = requestSpec.when().delete(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Perform DELETE request with path parameters
     * @param endpoint - API endpoint
     * @param pathParams - path parameters
     * @return Response
     */
    public Response delete(String endpoint, Map<String, Object> pathParams) {
        logger.info("Performing DELETE request to: {} with path params: {}", endpoint, pathParams);
        Response response = requestSpec.pathParams(pathParams).when().delete(endpoint);
        logResponse(response);
        return response;
    }
    
    /**
     * Add header to request
     * @param name - header name
     * @param value - header value
     * @return BaseApiClient
     */
    public BaseApiClient addHeader(String name, String value) {
        logger.debug("Adding header: {} = {}", name, value);
        requestSpec.header(name, value);
        return this;
    }
    
    /**
     * Add headers to request
     * @param headers - headers map
     * @return BaseApiClient
     */
    public BaseApiClient addHeaders(Map<String, String> headers) {
        logger.debug("Adding headers: {}", headers);
        requestSpec.headers(headers);
        return this;
    }
    
    /**
     * Add authorization header
     * @param token - authorization token
     * @return BaseApiClient
     */
    public BaseApiClient addAuthorizationHeader(String token) {
        logger.debug("Adding authorization header");
        requestSpec.header("Authorization", "Bearer " + token);
        return this;
    }
    
    /**
     * Set content type
     * @param contentType - content type
     * @return BaseApiClient
     */
    public BaseApiClient setContentType(ContentType contentType) {
        logger.debug("Setting content type: {}", contentType);
        requestSpec.contentType(contentType);
        return this;
    }
    
    /**
     * Reset request specification to default
     */
    public void resetRequestSpec() {
        logger.debug("Resetting request specification");
        setupRequestSpecification();
    }
    
    /**
     * Log response details
     * @param response - API response
     */
    private void logResponse(Response response) {
        logger.info("Response Status Code: {}", response.getStatusCode());
        logger.info("Response Time: {} ms", response.getTime());
        logger.debug("Response Body: {}", response.getBody().asString());
        logger.debug("Response Headers: {}", response.getHeaders());
    }
    
    /**
     * Get base URL
     * @return String - base URL
     */
    public String getBaseUrl() {
        return baseUrl;
    }
    
    /**
     * Set base URL
     * @param baseUrl - new base URL
     */
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
        logger.info("Base URL updated to: {}", baseUrl);
        setupRequestSpecification();
    }
}
