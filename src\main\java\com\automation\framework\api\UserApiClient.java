package com.automation.framework.api;

import io.restassured.response.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * User API Client for user-related API operations
 * Extends BaseApiClient to inherit common functionality
 */
public class UserApiClient extends BaseApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(UserApiClient.class);
    
    // API Endpoints
    private static final String USERS_ENDPOINT = "/users";
    private static final String USER_BY_ID_ENDPOINT = "/users/{id}";
    private static final String REGISTER_ENDPOINT = "/register";
    private static final String LOGIN_ENDPOINT = "/login";
    
    public UserApiClient(String baseUrl) {
        super(baseUrl);
        logger.info("UserApiClient initialized with base URL: {}", baseUrl);
    }
    
    /**
     * Get all users
     * @return Response
     */
    public Response getAllUsers() {
        logger.info("Getting all users");
        return get(USERS_ENDPOINT);
    }
    
    /**
     * Get users with pagination
     * @param page - page number
     * @return Response
     */
    public Response getUsersWithPagination(int page) {
        logger.info("Getting users for page: {}", page);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("page", page);
        return getWithQueryParams(USERS_ENDPOINT, queryParams);
    }
    
    /**
     * Get user by ID
     * @param userId - user ID
     * @return Response
     */
    public Response getUserById(int userId) {
        logger.info("Getting user by ID: {}", userId);
        Map<String, Object> pathParams = new HashMap<>();
        pathParams.put("id", userId);
        return get(USER_BY_ID_ENDPOINT, pathParams);
    }
    
    /**
     * Create new user
     * @param name - user name
     * @param job - user job
     * @return Response
     */
    public Response createUser(String name, String job) {
        logger.info("Creating user with name: {} and job: {}", name, job);
        Map<String, Object> userBody = new HashMap<>();
        userBody.put("name", name);
        userBody.put("job", job);
        return post(USERS_ENDPOINT, userBody);
    }
    
    /**
     * Create user with custom body
     * @param userBody - user data
     * @return Response
     */
    public Response createUser(Map<String, Object> userBody) {
        logger.info("Creating user with custom body: {}", userBody);
        return post(USERS_ENDPOINT, userBody);
    }
    
    /**
     * Update user
     * @param userId - user ID
     * @param name - updated name
     * @param job - updated job
     * @return Response
     */
    public Response updateUser(int userId, String name, String job) {
        logger.info("Updating user {} with name: {} and job: {}", userId, name, job);
        Map<String, Object> userBody = new HashMap<>();
        userBody.put("name", name);
        userBody.put("job", job);
        
        Map<String, Object> pathParams = new HashMap<>();
        pathParams.put("id", userId);
        
        return put(USER_BY_ID_ENDPOINT.replace("{id}", String.valueOf(userId)), userBody);
    }
    
    /**
     * Partially update user
     * @param userId - user ID
     * @param updateData - data to update
     * @return Response
     */
    public Response partialUpdateUser(int userId, Map<String, Object> updateData) {
        logger.info("Partially updating user {} with data: {}", userId, updateData);
        return patch(USER_BY_ID_ENDPOINT.replace("{id}", String.valueOf(userId)), updateData);
    }
    
    /**
     * Delete user
     * @param userId - user ID
     * @return Response
     */
    public Response deleteUser(int userId) {
        logger.info("Deleting user with ID: {}", userId);
        Map<String, Object> pathParams = new HashMap<>();
        pathParams.put("id", userId);
        return delete(USER_BY_ID_ENDPOINT, pathParams);
    }
    
    /**
     * Register user
     * @param email - user email
     * @param password - user password
     * @return Response
     */
    public Response registerUser(String email, String password) {
        logger.info("Registering user with email: {}", email);
        Map<String, Object> registerBody = new HashMap<>();
        registerBody.put("email", email);
        registerBody.put("password", password);
        return post(REGISTER_ENDPOINT, registerBody);
    }
    
    /**
     * Register user with only email (for testing validation)
     * @param email - user email
     * @return Response
     */
    public Response registerUserWithEmailOnly(String email) {
        logger.info("Registering user with email only: {}", email);
        Map<String, Object> registerBody = new HashMap<>();
        registerBody.put("email", email);
        return post(REGISTER_ENDPOINT, registerBody);
    }
    
    /**
     * Login user
     * @param email - user email
     * @param password - user password
     * @return Response
     */
    public Response loginUser(String email, String password) {
        logger.info("Logging in user with email: {}", email);
        Map<String, Object> loginBody = new HashMap<>();
        loginBody.put("email", email);
        loginBody.put("password", password);
        return post(LOGIN_ENDPOINT, loginBody);
    }
    
    /**
     * Login user with only email (for testing validation)
     * @param email - user email
     * @return Response
     */
    public Response loginUserWithEmailOnly(String email) {
        logger.info("Logging in user with email only: {}", email);
        Map<String, Object> loginBody = new HashMap<>();
        loginBody.put("email", email);
        return post(LOGIN_ENDPOINT, loginBody);
    }
    
    /**
     * Get user with delay (for testing timeouts)
     * @param userId - user ID
     * @param delay - delay in seconds
     * @return Response
     */
    public Response getUserWithDelay(int userId, int delay) {
        logger.info("Getting user {} with delay of {} seconds", userId, delay);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("delay", delay);
        return getWithQueryParams(USER_BY_ID_ENDPOINT.replace("{id}", String.valueOf(userId)), queryParams);
    }
}
