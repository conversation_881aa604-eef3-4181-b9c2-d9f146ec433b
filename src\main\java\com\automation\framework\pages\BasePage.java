package com.automation.framework.pages;

import net.serenitybdd.core.pages.PageObject;
import net.serenitybdd.core.pages.WebElementFacade;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.List;

/**
 * Base Page class that contains common methods and utilities
 * All page objects should extend this class
 */
public abstract class BasePage extends PageObject {
    
    private static final Logger logger = LoggerFactory.getLogger(BasePage.class);
    private static final int DEFAULT_TIMEOUT = 30;
    
    protected WebDriverWait wait;
    
    public BasePage(WebDriver driver) {
        super(driver);
        this.wait = new WebDriverWait(driver, Duration.ofSeconds(DEFAULT_TIMEOUT));
    }
    
    /**
     * Wait for element to be visible
     * @param locator - element locator
     * @return WebElement
     */
    protected WebElement waitForElementToBeVisible(By locator) {
        logger.debug("Waiting for element to be visible: {}", locator);
        return wait.until(ExpectedConditions.visibilityOfElementLocated(locator));
    }
    
    /**
     * Wait for element to be clickable
     * @param locator - element locator
     * @return WebElement
     */
    protected WebElement waitForElementToBeClickable(By locator) {
        logger.debug("Waiting for element to be clickable: {}", locator);
        return wait.until(ExpectedConditions.elementToBeClickable(locator));
    }
    
    /**
     * Wait for element to be present
     * @param locator - element locator
     * @return WebElement
     */
    protected WebElement waitForElementToBePresent(By locator) {
        logger.debug("Waiting for element to be present: {}", locator);
        return wait.until(ExpectedConditions.presenceOfElementLocated(locator));
    }
    
    /**
     * Wait for text to be present in element
     * @param locator - element locator
     * @param text - expected text
     * @return boolean
     */
    protected boolean waitForTextToBePresentInElement(By locator, String text) {
        logger.debug("Waiting for text '{}' to be present in element: {}", text, locator);
        return wait.until(ExpectedConditions.textToBePresentInElementLocated(locator, text));
    }
    
    /**
     * Click element using JavaScript
     * @param element - WebElement to click
     */
    protected void clickUsingJavaScript(WebElement element) {
        logger.debug("Clicking element using JavaScript");
        JavascriptExecutor js = (JavascriptExecutor) getDriver();
        js.executeScript("arguments[0].click();", element);
    }
    
    /**
     * Scroll to element
     * @param element - WebElement to scroll to
     */
    protected void scrollToElement(WebElement element) {
        logger.debug("Scrolling to element");
        JavascriptExecutor js = (JavascriptExecutor) getDriver();
        js.executeScript("arguments[0].scrollIntoView(true);", element);
    }
    
    /**
     * Get text from element with wait
     * @param locator - element locator
     * @return String - element text
     */
    protected String getTextWithWait(By locator) {
        WebElement element = waitForElementToBeVisible(locator);
        String text = element.getText();
        logger.debug("Retrieved text '{}' from element: {}", text, locator);
        return text;
    }
    
    /**
     * Type text into element with wait
     * @param locator - element locator
     * @param text - text to type
     */
    protected void typeTextWithWait(By locator, String text) {
        WebElement element = waitForElementToBeVisible(locator);
        element.clear();
        element.sendKeys(text);
        logger.debug("Typed text '{}' into element: {}", text, locator);
    }
    
    /**
     * Click element with wait
     * @param locator - element locator
     */
    protected void clickWithWait(By locator) {
        WebElement element = waitForElementToBeClickable(locator);
        element.click();
        logger.debug("Clicked element: {}", locator);
    }
    
    /**
     * Check if element is displayed
     * @param locator - element locator
     * @return boolean
     */
    protected boolean isElementDisplayed(By locator) {
        try {
            WebElement element = getDriver().findElement(locator);
            boolean isDisplayed = element.isDisplayed();
            logger.debug("Element {} is displayed: {}", locator, isDisplayed);
            return isDisplayed;
        } catch (Exception e) {
            logger.debug("Element {} is not displayed", locator);
            return false;
        }
    }
    
    /**
     * Get all elements matching locator
     * @param locator - element locator
     * @return List<WebElement>
     */
    protected List<WebElement> getElements(By locator) {
        logger.debug("Getting all elements matching: {}", locator);
        return getDriver().findElements(locator);
    }
    
    /**
     * Switch to frame
     * @param frameLocator - frame locator
     */
    protected void switchToFrame(By frameLocator) {
        WebElement frame = waitForElementToBePresent(frameLocator);
        getDriver().switchTo().frame(frame);
        logger.debug("Switched to frame: {}", frameLocator);
    }
    
    /**
     * Switch to default content
     */
    protected void switchToDefaultContent() {
        getDriver().switchTo().defaultContent();
        logger.debug("Switched to default content");
    }
    
    /**
     * Refresh the page
     */
    protected void refreshPage() {
        getDriver().navigate().refresh();
        logger.debug("Page refreshed");
    }
    
    /**
     * Get current page title
     * @return String - page title
     */
    protected String getPageTitle() {
        String title = getDriver().getTitle();
        logger.debug("Current page title: {}", title);
        return title;
    }
    
    /**
     * Get current page URL
     * @return String - current URL
     */
    protected String getCurrentUrl() {
        String url = getDriver().getCurrentUrl();
        logger.debug("Current URL: {}", url);
        return url;
    }
}
