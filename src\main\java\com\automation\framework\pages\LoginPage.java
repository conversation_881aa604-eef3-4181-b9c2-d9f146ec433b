package com.automation.framework.pages;

import net.serenitybdd.annotations.DefaultUrl;
import net.serenitybdd.core.annotations.findby.FindBy;
import net.serenitybdd.core.pages.WebElementFacade;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Login Page Object Model
 * Contains elements and methods for the login page
 */
@DefaultUrl("https://www.saucedemo.com")
public class LoginPage extends BasePage {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginPage.class);
    
    // Page Elements using Serenity annotations
    @FindBy(id = "user-name")
    private WebElementFacade usernameField;
    
    @FindBy(id = "password")
    private WebElementFacade passwordField;
    
    @FindBy(id = "login-button")
    private WebElementFacade loginButton;
    
    @FindBy(css = "[data-test='error']")
    private WebElementFacade errorMessage;
    
    @FindBy(className = "login_logo")
    private WebElementFacade loginLogo;
    
    // Alternative locators using By class
    private static final By USERNAME_FIELD = By.id("user-name");
    private static final By PASSWORD_FIELD = By.id("password");
    private static final By LOGIN_BUTTON = By.id("login-button");
    private static final By ERROR_MESSAGE = By.cssSelector("[data-test='error']");
    private static final By LOGIN_LOGO = By.className("login_logo");
    
    public LoginPage(WebDriver driver) {
        super(driver);
    }
    
    /**
     * Navigate to login page
     */
    public void navigateToLoginPage() {
        logger.info("Navigating to login page");
        open();
        waitForPageToLoad();
    }
    
    /**
     * Wait for login page to load
     */
    public void waitForPageToLoad() {
        logger.debug("Waiting for login page to load");
        waitForElementToBeVisible(LOGIN_LOGO);
        waitForElementToBeVisible(USERNAME_FIELD);
        waitForElementToBeVisible(PASSWORD_FIELD);
        waitForElementToBeVisible(LOGIN_BUTTON);
    }
    
    /**
     * Enter username
     * @param username - username to enter
     */
    public void enterUsername(String username) {
        logger.info("Entering username: {}", username);
        usernameField.waitUntilVisible().clear();
        usernameField.type(username);
    }
    
    /**
     * Enter password
     * @param password - password to enter
     */
    public void enterPassword(String password) {
        logger.info("Entering password");
        passwordField.waitUntilVisible().clear();
        passwordField.type(password);
    }
    
    /**
     * Click login button
     */
    public void clickLoginButton() {
        logger.info("Clicking login button");
        loginButton.waitUntilClickable().click();
    }
    
    /**
     * Perform login with username and password
     * @param username - username
     * @param password - password
     */
    public void login(String username, String password) {
        logger.info("Performing login with username: {}", username);
        enterUsername(username);
        enterPassword(password);
        clickLoginButton();
    }
    
    /**
     * Get error message text
     * @return String - error message
     */
    public String getErrorMessage() {
        logger.debug("Getting error message");
        if (errorMessage.isVisible()) {
            String message = errorMessage.getText();
            logger.debug("Error message: {}", message);
            return message;
        }
        return "";
    }
    
    /**
     * Check if error message is displayed
     * @return boolean - true if error message is displayed
     */
    public boolean isErrorMessageDisplayed() {
        boolean isDisplayed = errorMessage.isVisible();
        logger.debug("Error message displayed: {}", isDisplayed);
        return isDisplayed;
    }
    
    /**
     * Check if login page is displayed
     * @return boolean - true if login page is displayed
     */
    public boolean isLoginPageDisplayed() {
        boolean isDisplayed = loginLogo.isVisible() && 
                             usernameField.isVisible() && 
                             passwordField.isVisible() && 
                             loginButton.isVisible();
        logger.debug("Login page displayed: {}", isDisplayed);
        return isDisplayed;
    }
    
    /**
     * Get username field placeholder text
     * @return String - placeholder text
     */
    public String getUsernameFieldPlaceholder() {
        String placeholder = usernameField.getAttribute("placeholder");
        logger.debug("Username field placeholder: {}", placeholder);
        return placeholder;
    }
    
    /**
     * Get password field placeholder text
     * @return String - placeholder text
     */
    public String getPasswordFieldPlaceholder() {
        String placeholder = passwordField.getAttribute("placeholder");
        logger.debug("Password field placeholder: {}", placeholder);
        return placeholder;
    }
    
    /**
     * Clear username field
     */
    public void clearUsernameField() {
        logger.debug("Clearing username field");
        usernameField.clear();
    }
    
    /**
     * Clear password field
     */
    public void clearPasswordField() {
        logger.debug("Clearing password field");
        passwordField.clear();
    }
    
    /**
     * Clear all fields
     */
    public void clearAllFields() {
        logger.debug("Clearing all fields");
        clearUsernameField();
        clearPasswordField();
    }
    
    /**
     * Check if login button is enabled
     * @return boolean - true if login button is enabled
     */
    public boolean isLoginButtonEnabled() {
        boolean isEnabled = loginButton.isEnabled();
        logger.debug("Login button enabled: {}", isEnabled);
        return isEnabled;
    }
    
    /**
     * Get page title
     * @return String - page title
     */
    public String getLoginPageTitle() {
        return getPageTitle();
    }
}
