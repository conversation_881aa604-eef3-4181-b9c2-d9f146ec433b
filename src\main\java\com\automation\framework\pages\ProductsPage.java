package com.automation.framework.pages;

import net.serenitybdd.core.annotations.findby.FindBy;
import net.serenitybdd.core.pages.WebElementFacade;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Products Page Object Model
 * Contains elements and methods for the products/inventory page
 */
public class ProductsPage extends BasePage {
    
    private static final Logger logger = LoggerFactory.getLogger(ProductsPage.class);
    
    // Page Elements
    @FindBy(className = "title")
    private WebElementFacade pageTitle;
    
    @FindBy(className = "shopping_cart_link")
    private WebElementFacade shoppingCartLink;
    
    @FindBy(className = "shopping_cart_badge")
    private WebElementFacade shoppingCartBadge;
    
    @FindBy(id = "react-burger-menu-btn")
    private WebElementFacade menuButton;
    
    @FindBy(className = "product_sort_container")
    private WebElementFacade sortDropdown;
    
    @FindBy(className = "inventory_item")
    private List<WebElementFacade> productItems;
    
    @FindBy(css = ".inventory_item .inventory_item_name")
    private List<WebElementFacade> productNames;
    
    @FindBy(css = ".inventory_item .inventory_item_price")
    private List<WebElementFacade> productPrices;
    
    @FindBy(css = ".inventory_item button")
    private List<WebElementFacade> addToCartButtons;
    
    // Static locators
    private static final By PAGE_TITLE = By.className("title");
    private static final By SHOPPING_CART_LINK = By.className("shopping_cart_link");
    private static final By SHOPPING_CART_BADGE = By.className("shopping_cart_badge");
    private static final By MENU_BUTTON = By.id("react-burger-menu-btn");
    private static final By SORT_DROPDOWN = By.className("product_sort_container");
    private static final By PRODUCT_ITEMS = By.className("inventory_item");
    
    public ProductsPage(WebDriver driver) {
        super(driver);
    }
    
    /**
     * Wait for products page to load
     */
    public void waitForPageToLoad() {
        logger.debug("Waiting for products page to load");
        waitForElementToBeVisible(PAGE_TITLE);
        waitForElementToBeVisible(SHOPPING_CART_LINK);
        waitForElementToBeVisible(SORT_DROPDOWN);
    }
    
    /**
     * Check if products page is displayed
     * @return boolean - true if products page is displayed
     */
    public boolean isProductsPageDisplayed() {
        boolean isDisplayed = pageTitle.isVisible() && 
                             shoppingCartLink.isVisible() && 
                             sortDropdown.isVisible();
        logger.debug("Products page displayed: {}", isDisplayed);
        return isDisplayed;
    }
    
    /**
     * Get page title text
     * @return String - page title
     */
    public String getPageTitleText() {
        String title = pageTitle.getText();
        logger.debug("Page title: {}", title);
        return title;
    }
    
    /**
     * Get number of products displayed
     * @return int - number of products
     */
    public int getProductCount() {
        int count = productItems.size();
        logger.debug("Number of products: {}", count);
        return count;
    }
    
    /**
     * Get product name by index
     * @param index - product index (0-based)
     * @return String - product name
     */
    public String getProductName(int index) {
        if (index < productNames.size()) {
            String name = productNames.get(index).getText();
            logger.debug("Product {} name: {}", index, name);
            return name;
        }
        throw new IndexOutOfBoundsException("Product index " + index + " is out of bounds");
    }
    
    /**
     * Get product price by index
     * @param index - product index (0-based)
     * @return String - product price
     */
    public String getProductPrice(int index) {
        if (index < productPrices.size()) {
            String price = productPrices.get(index).getText();
            logger.debug("Product {} price: {}", index, price);
            return price;
        }
        throw new IndexOutOfBoundsException("Product index " + index + " is out of bounds");
    }
    
    /**
     * Add product to cart by index
     * @param index - product index (0-based)
     */
    public void addProductToCart(int index) {
        if (index < addToCartButtons.size()) {
            String productName = getProductName(index);
            logger.info("Adding product to cart: {}", productName);
            addToCartButtons.get(index).click();
        } else {
            throw new IndexOutOfBoundsException("Product index " + index + " is out of bounds");
        }
    }
    
    /**
     * Add product to cart by name
     * @param productName - name of the product to add
     */
    public void addProductToCartByName(String productName) {
        logger.info("Adding product to cart by name: {}", productName);
        for (int i = 0; i < productNames.size(); i++) {
            if (productNames.get(i).getText().equals(productName)) {
                addToCartButtons.get(i).click();
                return;
            }
        }
        throw new RuntimeException("Product not found: " + productName);
    }
    
    /**
     * Click shopping cart link
     */
    public void clickShoppingCart() {
        logger.info("Clicking shopping cart");
        shoppingCartLink.click();
    }
    
    /**
     * Get shopping cart badge count
     * @return String - cart badge count (empty if no badge)
     */
    public String getShoppingCartBadgeCount() {
        if (shoppingCartBadge.isVisible()) {
            String count = shoppingCartBadge.getText();
            logger.debug("Shopping cart badge count: {}", count);
            return count;
        }
        logger.debug("Shopping cart badge not visible");
        return "";
    }
    
    /**
     * Check if shopping cart badge is displayed
     * @return boolean - true if badge is displayed
     */
    public boolean isShoppingCartBadgeDisplayed() {
        boolean isDisplayed = shoppingCartBadge.isVisible();
        logger.debug("Shopping cart badge displayed: {}", isDisplayed);
        return isDisplayed;
    }
    
    /**
     * Click menu button
     */
    public void clickMenuButton() {
        logger.info("Clicking menu button");
        menuButton.click();
    }
    
    /**
     * Select sort option
     * @param sortOption - sort option to select
     */
    public void selectSortOption(String sortOption) {
        logger.info("Selecting sort option: {}", sortOption);
        sortDropdown.selectByVisibleText(sortOption);
    }
    
    /**
     * Get all product names
     * @return List<String> - list of all product names
     */
    public List<String> getAllProductNames() {
        List<String> names = productNames.stream()
                .map(WebElementFacade::getText)
                .toList();
        logger.debug("All product names: {}", names);
        return names;
    }
    
    /**
     * Get all product prices
     * @return List<String> - list of all product prices
     */
    public List<String> getAllProductPrices() {
        List<String> prices = productPrices.stream()
                .map(WebElementFacade::getText)
                .toList();
        logger.debug("All product prices: {}", prices);
        return prices;
    }
    
    /**
     * Check if product exists by name
     * @param productName - product name to check
     * @return boolean - true if product exists
     */
    public boolean isProductDisplayed(String productName) {
        boolean exists = productNames.stream()
                .anyMatch(element -> element.getText().equals(productName));
        logger.debug("Product '{}' displayed: {}", productName, exists);
        return exists;
    }
}
