package com.automation.framework.steps;

import com.automation.framework.api.UserApiClient;
import io.restassured.response.Response;
import net.serenitybdd.annotations.Step;
import net.serenitybdd.core.steps.ScenarioSteps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;

/**
 * API Steps class containing step definitions for API testing
 * This class can be used with both JUnit and Cucumber tests
 */
public class ApiSteps extends ScenarioSteps {
    
    private static final Logger logger = LoggerFactory.getLogger(ApiSteps.class);
    
    private UserApiClient userApiClient;
    private Response lastResponse;
    
    public ApiSteps() {
        // Initialize with default base URL from properties
        String baseUrl = System.getProperty("api.base.url", "https://reqres.in/api");
        this.userApiClient = new UserApiClient(baseUrl);
    }
    
    @Step("Set API base URL to: {0}")
    public void setApiBaseUrl(String baseUrl) {
        logger.info("Step: Set API base URL to: {}", baseUrl);
        this.userApiClient = new UserApiClient(baseUrl);
    }
    
    @Step("Get all users")
    public void getAllUsers() {
        logger.info("Step: Get all users");
        lastResponse = userApiClient.getAllUsers();
    }
    
    @Step("Get users for page: {0}")
    public void getUsersForPage(int page) {
        logger.info("Step: Get users for page: {}", page);
        lastResponse = userApiClient.getUsersWithPagination(page);
    }
    
    @Step("Get user by ID: {0}")
    public void getUserById(int userId) {
        logger.info("Step: Get user by ID: {}", userId);
        lastResponse = userApiClient.getUserById(userId);
    }
    
    @Step("Create user with name: {0} and job: {1}")
    public void createUser(String name, String job) {
        logger.info("Step: Create user with name: {} and job: {}", name, job);
        lastResponse = userApiClient.createUser(name, job);
    }
    
    @Step("Create user with data: {0}")
    public void createUserWithData(Map<String, Object> userData) {
        logger.info("Step: Create user with data: {}", userData);
        lastResponse = userApiClient.createUser(userData);
    }
    
    @Step("Update user {0} with name: {1} and job: {2}")
    public void updateUser(int userId, String name, String job) {
        logger.info("Step: Update user {} with name: {} and job: {}", userId, name, job);
        lastResponse = userApiClient.updateUser(userId, name, job);
    }
    
    @Step("Partially update user {0} with data: {1}")
    public void partiallyUpdateUser(int userId, Map<String, Object> updateData) {
        logger.info("Step: Partially update user {} with data: {}", userId, updateData);
        lastResponse = userApiClient.partialUpdateUser(userId, updateData);
    }
    
    @Step("Delete user with ID: {0}")
    public void deleteUser(int userId) {
        logger.info("Step: Delete user with ID: {}", userId);
        lastResponse = userApiClient.deleteUser(userId);
    }
    
    @Step("Register user with email: {0} and password: {1}")
    public void registerUser(String email, String password) {
        logger.info("Step: Register user with email: {}", email);
        lastResponse = userApiClient.registerUser(email, password);
    }
    
    @Step("Register user with email only: {0}")
    public void registerUserWithEmailOnly(String email) {
        logger.info("Step: Register user with email only: {}", email);
        lastResponse = userApiClient.registerUserWithEmailOnly(email);
    }
    
    @Step("Login user with email: {0} and password: {1}")
    public void loginUser(String email, String password) {
        logger.info("Step: Login user with email: {}", email);
        lastResponse = userApiClient.loginUser(email, password);
    }
    
    @Step("Login user with email only: {0}")
    public void loginUserWithEmailOnly(String email) {
        logger.info("Step: Login user with email only: {}", email);
        lastResponse = userApiClient.loginUserWithEmailOnly(email);
    }
    
    @Step("Get user {0} with delay of {1} seconds")
    public void getUserWithDelay(int userId, int delay) {
        logger.info("Step: Get user {} with delay of {} seconds", userId, delay);
        lastResponse = userApiClient.getUserWithDelay(userId, delay);
    }
    
    @Step("Verify response status code is: {0}")
    public void verifyResponseStatusCode(int expectedStatusCode) {
        logger.info("Step: Verify response status code is: {}", expectedStatusCode);
        assertThat(lastResponse.getStatusCode())
                .as("Response status code should be " + expectedStatusCode)
                .isEqualTo(expectedStatusCode);
    }
    
    @Step("Verify response contains field: {0}")
    public void verifyResponseContainsField(String fieldName) {
        logger.info("Step: Verify response contains field: {}", fieldName);
        lastResponse.then().body(fieldName, notNullValue());
    }
    
    @Step("Verify response field {0} equals: {1}")
    public void verifyResponseFieldEquals(String fieldName, Object expectedValue) {
        logger.info("Step: Verify response field {} equals: {}", fieldName, expectedValue);
        lastResponse.then().body(fieldName, equalTo(expectedValue));
    }
    
    @Step("Verify response field {0} contains: {1}")
    public void verifyResponseFieldContains(String fieldName, String expectedValue) {
        logger.info("Step: Verify response field {} contains: {}", fieldName, expectedValue);
        lastResponse.then().body(fieldName, containsString(expectedValue));
    }
    
    @Step("Verify response time is less than: {0} ms")
    public void verifyResponseTime(long maxTimeInMs) {
        logger.info("Step: Verify response time is less than: {} ms", maxTimeInMs);
        assertThat(lastResponse.getTime())
                .as("Response time should be less than " + maxTimeInMs + " ms")
                .isLessThan(maxTimeInMs);
    }
    
    @Step("Verify response contains {0} users")
    public void verifyResponseContainsUsers(int expectedUserCount) {
        logger.info("Step: Verify response contains {} users", expectedUserCount);
        List<Object> users = lastResponse.jsonPath().getList("data");
        assertThat(users)
                .as("Response should contain " + expectedUserCount + " users")
                .hasSize(expectedUserCount);
    }
    
    @Step("Verify user ID is: {0}")
    public void verifyUserId(int expectedId) {
        logger.info("Step: Verify user ID is: {}", expectedId);
        lastResponse.then().body("data.id", equalTo(expectedId));
    }
    
    @Step("Verify user email is: {0}")
    public void verifyUserEmail(String expectedEmail) {
        logger.info("Step: Verify user email is: {}", expectedEmail);
        lastResponse.then().body("data.email", equalTo(expectedEmail));
    }
    
    @Step("Verify user first name is: {0}")
    public void verifyUserFirstName(String expectedFirstName) {
        logger.info("Step: Verify user first name is: {}", expectedFirstName);
        lastResponse.then().body("data.first_name", equalTo(expectedFirstName));
    }
    
    @Step("Verify user last name is: {0}")
    public void verifyUserLastName(String expectedLastName) {
        logger.info("Step: Verify user last name is: {}", expectedLastName);
        lastResponse.then().body("data.last_name", equalTo(expectedLastName));
    }
    
    @Step("Verify created user name is: {0}")
    public void verifyCreatedUserName(String expectedName) {
        logger.info("Step: Verify created user name is: {}", expectedName);
        lastResponse.then().body("name", equalTo(expectedName));
    }
    
    @Step("Verify created user job is: {0}")
    public void verifyCreatedUserJob(String expectedJob) {
        logger.info("Step: Verify created user job is: {}", expectedJob);
        lastResponse.then().body("job", equalTo(expectedJob));
    }
    
    @Step("Verify response contains token")
    public void verifyResponseContainsToken() {
        logger.info("Step: Verify response contains token");
        lastResponse.then().body("token", notNullValue());
    }
    
    @Step("Verify response contains error message: {0}")
    public void verifyResponseContainsErrorMessage(String expectedError) {
        logger.info("Step: Verify response contains error message: {}", expectedError);
        lastResponse.then().body("error", equalTo(expectedError));
    }
    
    @Step("Verify response header {0} equals: {1}")
    public void verifyResponseHeader(String headerName, String expectedValue) {
        logger.info("Step: Verify response header {} equals: {}", headerName, expectedValue);
        assertThat(lastResponse.getHeader(headerName))
                .as("Response header " + headerName + " should equal " + expectedValue)
                .isEqualTo(expectedValue);
    }
    
    @Step("Get response as string")
    public String getResponseAsString() {
        logger.info("Step: Get response as string");
        return lastResponse.getBody().asString();
    }
    
    @Step("Get response field value: {0}")
    public Object getResponseFieldValue(String fieldName) {
        logger.info("Step: Get response field value: {}", fieldName);
        return lastResponse.jsonPath().get(fieldName);
    }
}
