package com.automation.framework.steps;

import com.automation.framework.pages.LoginPage;
import com.automation.framework.pages.ProductsPage;
import net.serenitybdd.annotations.Step;
import net.serenitybdd.core.steps.ScenarioSteps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Login Steps class containing step definitions for login functionality
 * This class can be used with both JUnit and Cucumber tests
 */
public class LoginSteps extends ScenarioSteps {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginSteps.class);
    
    private LoginPage loginPage;
    private ProductsPage productsPage;
    
    @Step("Navigate to login page")
    public void navigateToLoginPage() {
        logger.info("Step: Navigate to login page");
        loginPage.navigateToLoginPage();
    }
    
    @Step("Enter username: {0}")
    public void enterUsername(String username) {
        logger.info("Step: Enter username: {}", username);
        loginPage.enterUsername(username);
    }
    
    @Step("Enter password: {0}")
    public void enterPassword(String password) {
        logger.info("Step: Enter password");
        loginPage.enterPassword(password);
    }
    
    @Step("Click login button")
    public void clickLoginButton() {
        logger.info("Step: Click login button");
        loginPage.clickLoginButton();
    }
    
    @Step("Login with username: {0} and password: {1}")
    public void loginWithCredentials(String username, String password) {
        logger.info("Step: Login with username: {} and password", username);
        loginPage.login(username, password);
    }
    
    @Step("Verify login page is displayed")
    public void verifyLoginPageIsDisplayed() {
        logger.info("Step: Verify login page is displayed");
        assertThat(loginPage.isLoginPageDisplayed())
                .as("Login page should be displayed")
                .isTrue();
    }
    
    @Step("Verify successful login")
    public void verifySuccessfulLogin() {
        logger.info("Step: Verify successful login");
        productsPage.waitForPageToLoad();
        assertThat(productsPage.isProductsPageDisplayed())
                .as("Products page should be displayed after successful login")
                .isTrue();
        assertThat(productsPage.getPageTitleText())
                .as("Page title should be 'Products'")
                .isEqualTo("Products");
    }
    
    @Step("Verify login error message: {0}")
    public void verifyLoginErrorMessage(String expectedMessage) {
        logger.info("Step: Verify login error message: {}", expectedMessage);
        assertThat(loginPage.isErrorMessageDisplayed())
                .as("Error message should be displayed")
                .isTrue();
        assertThat(loginPage.getErrorMessage())
                .as("Error message should match expected message")
                .contains(expectedMessage);
    }
    
    @Step("Verify error message is displayed")
    public void verifyErrorMessageIsDisplayed() {
        logger.info("Step: Verify error message is displayed");
        assertThat(loginPage.isErrorMessageDisplayed())
                .as("Error message should be displayed")
                .isTrue();
    }
    
    @Step("Verify error message is not displayed")
    public void verifyErrorMessageIsNotDisplayed() {
        logger.info("Step: Verify error message is not displayed");
        assertThat(loginPage.isErrorMessageDisplayed())
                .as("Error message should not be displayed")
                .isFalse();
    }
    
    @Step("Clear username field")
    public void clearUsernameField() {
        logger.info("Step: Clear username field");
        loginPage.clearUsernameField();
    }
    
    @Step("Clear password field")
    public void clearPasswordField() {
        logger.info("Step: Clear password field");
        loginPage.clearPasswordField();
    }
    
    @Step("Clear all login fields")
    public void clearAllLoginFields() {
        logger.info("Step: Clear all login fields");
        loginPage.clearAllFields();
    }
    
    @Step("Verify login button is enabled")
    public void verifyLoginButtonIsEnabled() {
        logger.info("Step: Verify login button is enabled");
        assertThat(loginPage.isLoginButtonEnabled())
                .as("Login button should be enabled")
                .isTrue();
    }
    
    @Step("Verify login button is disabled")
    public void verifyLoginButtonIsDisabled() {
        logger.info("Step: Verify login button is disabled");
        assertThat(loginPage.isLoginButtonEnabled())
                .as("Login button should be disabled")
                .isFalse();
    }
    
    @Step("Verify page title contains: {0}")
    public void verifyPageTitleContains(String expectedTitle) {
        logger.info("Step: Verify page title contains: {}", expectedTitle);
        String actualTitle = loginPage.getLoginPageTitle();
        assertThat(actualTitle)
                .as("Page title should contain expected text")
                .containsIgnoringCase(expectedTitle);
    }
    
    @Step("Get username field placeholder text")
    public String getUsernameFieldPlaceholder() {
        logger.info("Step: Get username field placeholder text");
        return loginPage.getUsernameFieldPlaceholder();
    }
    
    @Step("Get password field placeholder text")
    public String getPasswordFieldPlaceholder() {
        logger.info("Step: Get password field placeholder text");
        return loginPage.getPasswordFieldPlaceholder();
    }
    
    @Step("Verify username field placeholder is: {0}")
    public void verifyUsernameFieldPlaceholder(String expectedPlaceholder) {
        logger.info("Step: Verify username field placeholder is: {}", expectedPlaceholder);
        String actualPlaceholder = getUsernameFieldPlaceholder();
        assertThat(actualPlaceholder)
                .as("Username field placeholder should match expected value")
                .isEqualTo(expectedPlaceholder);
    }
    
    @Step("Verify password field placeholder is: {0}")
    public void verifyPasswordFieldPlaceholder(String expectedPlaceholder) {
        logger.info("Step: Verify password field placeholder is: {}", expectedPlaceholder);
        String actualPlaceholder = getPasswordFieldPlaceholder();
        assertThat(actualPlaceholder)
                .as("Password field placeholder should match expected value")
                .isEqualTo(expectedPlaceholder);
    }
}
