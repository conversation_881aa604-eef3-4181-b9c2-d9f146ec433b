package com.automation.framework.steps;

import com.automation.framework.pages.ProductsPage;
import net.serenitybdd.annotations.Step;
import net.serenitybdd.core.steps.ScenarioSteps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Product Steps class containing step definitions for product functionality
 * This class can be used with both JUnit and Cucumber tests
 */
public class ProductSteps extends ScenarioSteps {
    
    private static final Logger logger = LoggerFactory.getLogger(ProductSteps.class);
    
    private ProductsPage productsPage;
    
    @Step("Verify products page is displayed")
    public void verifyProductsPageIsDisplayed() {
        logger.info("Step: Verify products page is displayed");
        productsPage.waitForPageToLoad();
        assertThat(productsPage.isProductsPageDisplayed())
                .as("Products page should be displayed")
                .isTrue();
    }
    
    @Step("Verify page title is: {0}")
    public void verifyPageTitle(String expectedTitle) {
        logger.info("Step: Verify page title is: {}", expectedTitle);
        String actualTitle = productsPage.getPageTitleText();
        assertThat(actualTitle)
                .as("Page title should match expected value")
                .isEqualTo(expectedTitle);
    }
    
    @Step("Verify number of products is: {0}")
    public void verifyNumberOfProducts(int expectedCount) {
        logger.info("Step: Verify number of products is: {}", expectedCount);
        int actualCount = productsPage.getProductCount();
        assertThat(actualCount)
                .as("Number of products should match expected count")
                .isEqualTo(expectedCount);
    }
    
    @Step("Add product to cart by index: {0}")
    public void addProductToCartByIndex(int index) {
        logger.info("Step: Add product to cart by index: {}", index);
        productsPage.addProductToCart(index);
    }
    
    @Step("Add product to cart by name: {0}")
    public void addProductToCartByName(String productName) {
        logger.info("Step: Add product to cart by name: {}", productName);
        productsPage.addProductToCartByName(productName);
    }
    
    @Step("Click shopping cart")
    public void clickShoppingCart() {
        logger.info("Step: Click shopping cart");
        productsPage.clickShoppingCart();
    }
    
    @Step("Verify shopping cart badge count is: {0}")
    public void verifyShoppingCartBadgeCount(String expectedCount) {
        logger.info("Step: Verify shopping cart badge count is: {}", expectedCount);
        String actualCount = productsPage.getShoppingCartBadgeCount();
        assertThat(actualCount)
                .as("Shopping cart badge count should match expected value")
                .isEqualTo(expectedCount);
    }
    
    @Step("Verify shopping cart badge is displayed")
    public void verifyShoppingCartBadgeIsDisplayed() {
        logger.info("Step: Verify shopping cart badge is displayed");
        assertThat(productsPage.isShoppingCartBadgeDisplayed())
                .as("Shopping cart badge should be displayed")
                .isTrue();
    }
    
    @Step("Verify shopping cart badge is not displayed")
    public void verifyShoppingCartBadgeIsNotDisplayed() {
        logger.info("Step: Verify shopping cart badge is not displayed");
        assertThat(productsPage.isShoppingCartBadgeDisplayed())
                .as("Shopping cart badge should not be displayed")
                .isFalse();
    }
    
    @Step("Click menu button")
    public void clickMenuButton() {
        logger.info("Step: Click menu button");
        productsPage.clickMenuButton();
    }
    
    @Step("Select sort option: {0}")
    public void selectSortOption(String sortOption) {
        logger.info("Step: Select sort option: {}", sortOption);
        productsPage.selectSortOption(sortOption);
    }
    
    @Step("Get product name by index: {0}")
    public String getProductNameByIndex(int index) {
        logger.info("Step: Get product name by index: {}", index);
        return productsPage.getProductName(index);
    }
    
    @Step("Get product price by index: {0}")
    public String getProductPriceByIndex(int index) {
        logger.info("Step: Get product price by index: {}", index);
        return productsPage.getProductPrice(index);
    }
    
    @Step("Verify product name at index {0} is: {1}")
    public void verifyProductNameAtIndex(int index, String expectedName) {
        logger.info("Step: Verify product name at index {} is: {}", index, expectedName);
        String actualName = getProductNameByIndex(index);
        assertThat(actualName)
                .as("Product name at index " + index + " should match expected value")
                .isEqualTo(expectedName);
    }
    
    @Step("Verify product price at index {0} is: {1}")
    public void verifyProductPriceAtIndex(int index, String expectedPrice) {
        logger.info("Step: Verify product price at index {} is: {}", index, expectedPrice);
        String actualPrice = getProductPriceByIndex(index);
        assertThat(actualPrice)
                .as("Product price at index " + index + " should match expected value")
                .isEqualTo(expectedPrice);
    }
    
    @Step("Get all product names")
    public List<String> getAllProductNames() {
        logger.info("Step: Get all product names");
        return productsPage.getAllProductNames();
    }
    
    @Step("Get all product prices")
    public List<String> getAllProductPrices() {
        logger.info("Step: Get all product prices");
        return productsPage.getAllProductPrices();
    }
    
    @Step("Verify product is displayed: {0}")
    public void verifyProductIsDisplayed(String productName) {
        logger.info("Step: Verify product is displayed: {}", productName);
        assertThat(productsPage.isProductDisplayed(productName))
                .as("Product '" + productName + "' should be displayed")
                .isTrue();
    }
    
    @Step("Verify product is not displayed: {0}")
    public void verifyProductIsNotDisplayed(String productName) {
        logger.info("Step: Verify product is not displayed: {}", productName);
        assertThat(productsPage.isProductDisplayed(productName))
                .as("Product '" + productName + "' should not be displayed")
                .isFalse();
    }
    
    @Step("Verify products are sorted alphabetically")
    public void verifyProductsAreSortedAlphabetically() {
        logger.info("Step: Verify products are sorted alphabetically");
        List<String> productNames = getAllProductNames();
        List<String> sortedNames = productNames.stream()
                .sorted()
                .toList();
        assertThat(productNames)
                .as("Products should be sorted alphabetically")
                .isEqualTo(sortedNames);
    }
    
    @Step("Verify products are sorted by price low to high")
    public void verifyProductsAreSortedByPriceLowToHigh() {
        logger.info("Step: Verify products are sorted by price low to high");
        List<String> productPrices = getAllProductPrices();
        List<Double> prices = productPrices.stream()
                .map(price -> Double.parseDouble(price.replace("$", "")))
                .toList();
        List<Double> sortedPrices = prices.stream()
                .sorted()
                .toList();
        assertThat(prices)
                .as("Products should be sorted by price from low to high")
                .isEqualTo(sortedPrices);
    }
    
    @Step("Verify products are sorted by price high to low")
    public void verifyProductsAreSortedByPriceHighToLow() {
        logger.info("Step: Verify products are sorted by price high to low");
        List<String> productPrices = getAllProductPrices();
        List<Double> prices = productPrices.stream()
                .map(price -> Double.parseDouble(price.replace("$", "")))
                .toList();
        List<Double> sortedPrices = prices.stream()
                .sorted((a, b) -> Double.compare(b, a))
                .toList();
        assertThat(prices)
                .as("Products should be sorted by price from high to low")
                .isEqualTo(sortedPrices);
    }
}
