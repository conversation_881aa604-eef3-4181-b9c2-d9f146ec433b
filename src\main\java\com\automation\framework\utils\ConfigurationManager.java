package com.automation.framework.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Configuration Manager for handling application properties
 * Loads and manages configuration from properties files
 */
public class ConfigurationManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationManager.class);
    private static ConfigurationManager instance;
    private Properties properties;
    private Properties environmentProperties;
    
    private static final String SERENITY_PROPERTIES = "serenity.properties";
    private static final String ENVIRONMENT_PROPERTIES = "config/environments.properties";
    
    private ConfigurationManager() {
        loadProperties();
        loadEnvironmentProperties();
    }
    
    /**
     * Get singleton instance
     * @return ConfigurationManager instance
     */
    public static ConfigurationManager getInstance() {
        if (instance == null) {
            synchronized (ConfigurationManager.class) {
                if (instance == null) {
                    instance = new ConfigurationManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * Load main properties file
     */
    private void loadProperties() {
        properties = new Properties();
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(SERENITY_PROPERTIES)) {
            if (inputStream != null) {
                properties.load(inputStream);
                logger.info("Loaded properties from: {}", SERENITY_PROPERTIES);
            } else {
                logger.warn("Properties file not found: {}", SERENITY_PROPERTIES);
            }
        } catch (IOException e) {
            logger.error("Error loading properties file: {}", SERENITY_PROPERTIES, e);
        }
    }
    
    /**
     * Load environment-specific properties
     */
    private void loadEnvironmentProperties() {
        environmentProperties = new Properties();
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(ENVIRONMENT_PROPERTIES)) {
            if (inputStream != null) {
                environmentProperties.load(inputStream);
                logger.info("Loaded environment properties from: {}", ENVIRONMENT_PROPERTIES);
            } else {
                logger.warn("Environment properties file not found: {}", ENVIRONMENT_PROPERTIES);
            }
        } catch (IOException e) {
            logger.error("Error loading environment properties file: {}", ENVIRONMENT_PROPERTIES, e);
        }
    }
    
    /**
     * Get property value
     * @param key - property key
     * @return String - property value
     */
    public String getProperty(String key) {
        // First check system properties
        String value = System.getProperty(key);
        if (value != null) {
            logger.debug("Found system property: {} = {}", key, value);
            return value;
        }
        
        // Then check main properties
        value = properties.getProperty(key);
        if (value != null) {
            logger.debug("Found property: {} = {}", key, value);
            return value;
        }
        
        // Finally check environment properties
        value = environmentProperties.getProperty(key);
        if (value != null) {
            logger.debug("Found environment property: {} = {}", key, value);
            return value;
        }
        
        logger.debug("Property not found: {}", key);
        return null;
    }
    
    /**
     * Get property value with default
     * @param key - property key
     * @param defaultValue - default value if property not found
     * @return String - property value or default
     */
    public String getProperty(String key, String defaultValue) {
        String value = getProperty(key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * Get boolean property
     * @param key - property key
     * @return boolean - property value as boolean
     */
    public boolean getBooleanProperty(String key) {
        String value = getProperty(key);
        return Boolean.parseBoolean(value);
    }
    
    /**
     * Get boolean property with default
     * @param key - property key
     * @param defaultValue - default value
     * @return boolean - property value or default
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = getProperty(key);
        return value != null ? Boolean.parseBoolean(value) : defaultValue;
    }
    
    /**
     * Get integer property
     * @param key - property key
     * @return int - property value as integer
     */
    public int getIntProperty(String key) {
        String value = getProperty(key);
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.error("Error parsing integer property: {} = {}", key, value, e);
            return 0;
        }
    }
    
    /**
     * Get integer property with default
     * @param key - property key
     * @param defaultValue - default value
     * @return int - property value or default
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.error("Error parsing integer property: {} = {}", key, value, e);
            return defaultValue;
        }
    }
    
    /**
     * Get environment-specific property
     * @param environment - environment name
     * @param key - property key
     * @return String - environment-specific property value
     */
    public String getEnvironmentProperty(String environment, String key) {
        String envKey = environment + "." + key;
        return getProperty(envKey);
    }
    
    /**
     * Get current environment
     * @return String - current environment
     */
    public String getCurrentEnvironment() {
        return getProperty("environments.default", "local");
    }
    
    /**
     * Get base URL for current environment
     * @return String - base URL
     */
    public String getBaseUrl() {
        String environment = getCurrentEnvironment();
        String baseUrl = getEnvironmentProperty(environment, "base.url");
        return baseUrl != null ? baseUrl : getProperty("webdriver.base.url");
    }
    
    /**
     * Get API base URL for current environment
     * @return String - API base URL
     */
    public String getApiBaseUrl() {
        String environment = getCurrentEnvironment();
        String apiBaseUrl = getEnvironmentProperty(environment, "api.url");
        return apiBaseUrl != null ? apiBaseUrl : getProperty("api.base.url");
    }
    
    /**
     * Get browser for current environment
     * @return String - browser name
     */
    public String getBrowser() {
        String environment = getCurrentEnvironment();
        String browser = getEnvironmentProperty(environment, "browser");
        return browser != null ? browser : getProperty("webdriver.driver", "chrome");
    }
    
    /**
     * Check if headless mode is enabled
     * @return boolean - true if headless mode is enabled
     */
    public boolean isHeadlessMode() {
        String environment = getCurrentEnvironment();
        String headless = getEnvironmentProperty(environment, "headless");
        if (headless != null) {
            return Boolean.parseBoolean(headless);
        }
        return getBooleanProperty("headless.mode", false);
    }
    
    /**
     * Get timeout value
     * @return int - timeout in seconds
     */
    public int getTimeout() {
        String environment = getCurrentEnvironment();
        String timeout = getEnvironmentProperty(environment, "timeout");
        if (timeout != null) {
            return Integer.parseInt(timeout);
        }
        return getIntProperty("webdriver.wait.for.timeout", 30000) / 1000;
    }
}
