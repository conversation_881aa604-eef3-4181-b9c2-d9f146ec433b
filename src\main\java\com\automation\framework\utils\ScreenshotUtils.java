package com.automation.framework.utils;

import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Screenshot Utilities for capturing and managing screenshots
 * Provides methods to take screenshots and save them with meaningful names
 */
public class ScreenshotUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ScreenshotUtils.class);
    private static final String SCREENSHOT_DIR = "target/screenshots";
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
    
    /**
     * Take screenshot and save to file
     * @param driver - WebDriver instance
     * @param testName - name of the test
     * @return String - path to screenshot file
     */
    public static String takeScreenshot(WebDriver driver, String testName) {
        try {
            // Create screenshots directory if it doesn't exist
            createScreenshotDirectory();
            
            // Take screenshot
            TakesScreenshot takesScreenshot = (TakesScreenshot) driver;
            File sourceFile = takesScreenshot.getScreenshotAs(OutputType.FILE);
            
            // Generate filename with timestamp
            String timestamp = LocalDateTime.now().format(DATE_FORMAT);
            String fileName = String.format("%s_%s.png", testName, timestamp);
            String filePath = SCREENSHOT_DIR + File.separator + fileName;
            
            // Copy screenshot to target location
            Path targetPath = Paths.get(filePath);
            Files.copy(sourceFile.toPath(), targetPath);
            
            logger.info("Screenshot saved: {}", filePath);
            return filePath;
            
        } catch (IOException e) {
            logger.error("Error taking screenshot for test: {}", testName, e);
            return null;
        }
    }
    
    /**
     * Take screenshot on test failure
     * @param driver - WebDriver instance
     * @param testName - name of the failed test
     * @return String - path to screenshot file
     */
    public static String takeScreenshotOnFailure(WebDriver driver, String testName) {
        String fileName = "FAILED_" + testName;
        logger.warn("Taking screenshot for failed test: {}", testName);
        return takeScreenshot(driver, fileName);
    }
    
    /**
     * Take screenshot with custom filename
     * @param driver - WebDriver instance
     * @param fileName - custom filename (without extension)
     * @return String - path to screenshot file
     */
    public static String takeScreenshotWithCustomName(WebDriver driver, String fileName) {
        try {
            createScreenshotDirectory();
            
            TakesScreenshot takesScreenshot = (TakesScreenshot) driver;
            File sourceFile = takesScreenshot.getScreenshotAs(OutputType.FILE);
            
            String timestamp = LocalDateTime.now().format(DATE_FORMAT);
            String fullFileName = String.format("%s_%s.png", fileName, timestamp);
            String filePath = SCREENSHOT_DIR + File.separator + fullFileName;
            
            Path targetPath = Paths.get(filePath);
            Files.copy(sourceFile.toPath(), targetPath);
            
            logger.info("Screenshot saved with custom name: {}", filePath);
            return filePath;
            
        } catch (IOException e) {
            logger.error("Error taking screenshot with custom name: {}", fileName, e);
            return null;
        }
    }
    
    /**
     * Take screenshot as byte array
     * @param driver - WebDriver instance
     * @return byte[] - screenshot as byte array
     */
    public static byte[] takeScreenshotAsBytes(WebDriver driver) {
        try {
            TakesScreenshot takesScreenshot = (TakesScreenshot) driver;
            byte[] screenshot = takesScreenshot.getScreenshotAs(OutputType.BYTES);
            logger.debug("Screenshot taken as byte array");
            return screenshot;
        } catch (Exception e) {
            logger.error("Error taking screenshot as bytes", e);
            return new byte[0];
        }
    }
    
    /**
     * Take screenshot as base64 string
     * @param driver - WebDriver instance
     * @return String - screenshot as base64 string
     */
    public static String takeScreenshotAsBase64(WebDriver driver) {
        try {
            TakesScreenshot takesScreenshot = (TakesScreenshot) driver;
            String screenshot = takesScreenshot.getScreenshotAs(OutputType.BASE64);
            logger.debug("Screenshot taken as base64 string");
            return screenshot;
        } catch (Exception e) {
            logger.error("Error taking screenshot as base64", e);
            return "";
        }
    }
    
    /**
     * Create screenshot directory if it doesn't exist
     */
    private static void createScreenshotDirectory() {
        try {
            Path screenshotPath = Paths.get(SCREENSHOT_DIR);
            if (!Files.exists(screenshotPath)) {
                Files.createDirectories(screenshotPath);
                logger.debug("Created screenshot directory: {}", SCREENSHOT_DIR);
            }
        } catch (IOException e) {
            logger.error("Error creating screenshot directory: {}", SCREENSHOT_DIR, e);
        }
    }
    
    /**
     * Clean up old screenshots (older than specified days)
     * @param daysToKeep - number of days to keep screenshots
     */
    public static void cleanupOldScreenshots(int daysToKeep) {
        try {
            Path screenshotPath = Paths.get(SCREENSHOT_DIR);
            if (!Files.exists(screenshotPath)) {
                return;
            }
            
            long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24L * 60L * 60L * 1000L);
            
            Files.list(screenshotPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".png"))
                    .filter(path -> {
                        try {
                            return Files.getLastModifiedTime(path).toMillis() < cutoffTime;
                        } catch (IOException e) {
                            logger.error("Error checking file modification time: {}", path, e);
                            return false;
                        }
                    })
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            logger.debug("Deleted old screenshot: {}", path);
                        } catch (IOException e) {
                            logger.error("Error deleting old screenshot: {}", path, e);
                        }
                    });
            
            logger.info("Cleaned up screenshots older than {} days", daysToKeep);
            
        } catch (IOException e) {
            logger.error("Error cleaning up old screenshots", e);
        }
    }
    
    /**
     * Get screenshot directory path
     * @return String - screenshot directory path
     */
    public static String getScreenshotDirectory() {
        return SCREENSHOT_DIR;
    }
    
    /**
     * Check if driver supports screenshots
     * @param driver - WebDriver instance
     * @return boolean - true if driver supports screenshots
     */
    public static boolean supportsScreenshots(WebDriver driver) {
        try {
            return driver instanceof TakesScreenshot;
        } catch (Exception e) {
            logger.error("Error checking screenshot support", e);
            return false;
        }
    }
}
