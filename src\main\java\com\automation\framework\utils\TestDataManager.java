package com.automation.framework.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * Test Data Manager for handling test data
 * Provides methods to load and access test data from various sources
 */
public class TestDataManager {
    
    private static final Logger logger = LoggerFactory.getLogger(TestDataManager.class);
    private static TestDataManager instance;
    private Map<String, Properties> testDataCache;
    
    private TestDataManager() {
        testDataCache = new HashMap<>();
        loadDefaultTestData();
    }
    
    /**
     * Get singleton instance
     * @return TestDataManager instance
     */
    public static TestDataManager getInstance() {
        if (instance == null) {
            synchronized (TestDataManager.class) {
                if (instance == null) {
                    instance = new TestDataManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * Load default test data
     */
    private void loadDefaultTestData() {
        loadTestDataFromFile("users.properties");
        loadTestDataFromFile("products.properties");
        loadTestDataFromFile("api.properties");
    }
    
    /**
     * Load test data from properties file
     * @param fileName - properties file name
     */
    public void loadTestDataFromFile(String fileName) {
        Properties properties = new Properties();
        String filePath = "testdata/" + fileName;
        
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(filePath)) {
            if (inputStream != null) {
                properties.load(inputStream);
                testDataCache.put(fileName, properties);
                logger.info("Loaded test data from: {}", filePath);
            } else {
                logger.warn("Test data file not found: {}", filePath);
            }
        } catch (IOException e) {
            logger.error("Error loading test data file: {}", filePath, e);
        }
    }
    
    /**
     * Get test data value
     * @param fileName - properties file name
     * @param key - data key
     * @return String - test data value
     */
    public String getTestData(String fileName, String key) {
        Properties properties = testDataCache.get(fileName);
        if (properties != null) {
            String value = properties.getProperty(key);
            logger.debug("Retrieved test data: {} = {} from {}", key, value, fileName);
            return value;
        }
        logger.warn("Test data file not loaded: {}", fileName);
        return null;
    }
    
    /**
     * Get test data value with default
     * @param fileName - properties file name
     * @param key - data key
     * @param defaultValue - default value
     * @return String - test data value or default
     */
    public String getTestData(String fileName, String key, String defaultValue) {
        String value = getTestData(fileName, key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * Get user credentials
     * @param userType - type of user (standard, locked, problem, etc.)
     * @return Map<String, String> - username and password
     */
    public Map<String, String> getUserCredentials(String userType) {
        Map<String, String> credentials = new HashMap<>();
        String username = getTestData("users.properties", userType + ".username");
        String password = getTestData("users.properties", userType + ".password");
        
        if (username != null && password != null) {
            credentials.put("username", username);
            credentials.put("password", password);
            logger.debug("Retrieved credentials for user type: {}", userType);
        } else {
            logger.warn("Credentials not found for user type: {}", userType);
        }
        
        return credentials;
    }
    
    /**
     * Get standard user credentials
     * @return Map<String, String> - standard user credentials
     */
    public Map<String, String> getStandardUserCredentials() {
        return getUserCredentials("standard");
    }
    
    /**
     * Get locked user credentials
     * @return Map<String, String> - locked user credentials
     */
    public Map<String, String> getLockedUserCredentials() {
        return getUserCredentials("locked");
    }
    
    /**
     * Get problem user credentials
     * @return Map<String, String> - problem user credentials
     */
    public Map<String, String> getProblemUserCredentials() {
        return getUserCredentials("problem");
    }
    
    /**
     * Get product data
     * @param productKey - product key
     * @return String - product data
     */
    public String getProductData(String productKey) {
        return getTestData("products.properties", productKey);
    }
    
    /**
     * Get API test data
     * @param apiKey - API data key
     * @return String - API test data
     */
    public String getApiTestData(String apiKey) {
        return getTestData("api.properties", apiKey);
    }
    
    /**
     * Get all test data from a file
     * @param fileName - properties file name
     * @return Properties - all properties from the file
     */
    public Properties getAllTestData(String fileName) {
        Properties properties = testDataCache.get(fileName);
        if (properties != null) {
            logger.debug("Retrieved all test data from: {}", fileName);
            return new Properties(properties); // Return a copy
        }
        logger.warn("Test data file not loaded: {}", fileName);
        return new Properties();
    }
    
    /**
     * Check if test data exists
     * @param fileName - properties file name
     * @param key - data key
     * @return boolean - true if data exists
     */
    public boolean hasTestData(String fileName, String key) {
        Properties properties = testDataCache.get(fileName);
        if (properties != null) {
            boolean exists = properties.containsKey(key);
            logger.debug("Test data exists: {} in {} = {}", key, fileName, exists);
            return exists;
        }
        return false;
    }
    
    /**
     * Generate random email
     * @return String - random email address
     */
    public String generateRandomEmail() {
        long timestamp = System.currentTimeMillis();
        String email = "test" + timestamp + "@example.com";
        logger.debug("Generated random email: {}", email);
        return email;
    }
    
    /**
     * Generate random username
     * @return String - random username
     */
    public String generateRandomUsername() {
        long timestamp = System.currentTimeMillis();
        String username = "user" + timestamp;
        logger.debug("Generated random username: {}", username);
        return username;
    }
    
    /**
     * Generate random string
     * @param prefix - string prefix
     * @return String - random string with prefix
     */
    public String generateRandomString(String prefix) {
        long timestamp = System.currentTimeMillis();
        String randomString = prefix + timestamp;
        logger.debug("Generated random string: {}", randomString);
        return randomString;
    }
}
