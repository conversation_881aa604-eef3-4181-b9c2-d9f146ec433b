package com.automation.framework;

import com.automation.framework.steps.ApiSteps;
import net.serenitybdd.annotations.Steps;
import net.serenitybdd.junit5.SerenityJUnit5Extension;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.HashMap;
import java.util.Map;

/**
 * JUnit 5 test class for API functionality using Serenity BDD and REST Assured
 * Demonstrates API testing capabilities of the framework
 */
@ExtendWith(SerenityJUnit5Extension.class)
@DisplayName("API Tests")
@Tag("api")
public class ApiTest {
    
    @Steps
    ApiSteps apiSteps;
    
    @Test
    @DisplayName("Get all users")
    @Tag("positive")
    @Tag("critical")
    public void testGetAllUsers() {
        apiSteps.getAllUsers();
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyResponseContainsField("data");
        apiSteps.verifyResponseTime(5000);
    }
    
    @Test
    @DisplayName("Get users with pagination")
    @Tag("positive")
    public void testGetUsersWithPagination() {
        apiSteps.getUsersForPage(2);
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyResponseFieldEquals("page", 2);
        apiSteps.verifyResponseContainsField("data");
        apiSteps.verifyResponseContainsField("total");
    }
    
    @ParameterizedTest
    @DisplayName("Get user by different IDs")
    @ValueSource(ints = {1, 2, 3, 4, 5})
    @Tag("positive")
    public void testGetUserById(int userId) {
        apiSteps.getUserById(userId);
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyUserId(userId);
        apiSteps.verifyResponseContainsField("data.email");
        apiSteps.verifyResponseContainsField("data.first_name");
        apiSteps.verifyResponseContainsField("data.last_name");
    }
    
    @Test
    @DisplayName("Get non-existent user")
    @Tag("negative")
    public void testGetNonExistentUser() {
        apiSteps.getUserById(999);
        apiSteps.verifyResponseStatusCode(404);
    }
    
    @Test
    @DisplayName("Create new user")
    @Tag("positive")
    @Tag("critical")
    public void testCreateUser() {
        apiSteps.createUser("John Doe", "Software Engineer");
        apiSteps.verifyResponseStatusCode(201);
        apiSteps.verifyCreatedUserName("John Doe");
        apiSteps.verifyCreatedUserJob("Software Engineer");
        apiSteps.verifyResponseContainsField("id");
        apiSteps.verifyResponseContainsField("createdAt");
    }
    
    @Test
    @DisplayName("Create user with custom data")
    @Tag("positive")
    public void testCreateUserWithCustomData() {
        Map<String, Object> userData = new HashMap<>();
        userData.put("name", "Jane Smith");
        userData.put("job", "QA Engineer");
        userData.put("location", "New York");
        
        apiSteps.createUserWithData(userData);
        apiSteps.verifyResponseStatusCode(201);
        apiSteps.verifyCreatedUserName("Jane Smith");
        apiSteps.verifyCreatedUserJob("QA Engineer");
    }
    
    @Test
    @DisplayName("Update existing user")
    @Tag("positive")
    public void testUpdateUser() {
        apiSteps.updateUser(2, "Updated Name", "Updated Job");
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyCreatedUserName("Updated Name");
        apiSteps.verifyCreatedUserJob("Updated Job");
        apiSteps.verifyResponseContainsField("updatedAt");
    }
    
    @Test
    @DisplayName("Partially update user")
    @Tag("positive")
    public void testPartiallyUpdateUser() {
        Map<String, Object> updateData = new HashMap<>();
        updateData.put("job", "Senior Developer");
        
        apiSteps.partiallyUpdateUser(2, updateData);
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyCreatedUserJob("Senior Developer");
        apiSteps.verifyResponseContainsField("updatedAt");
    }
    
    @Test
    @DisplayName("Delete user")
    @Tag("positive")
    public void testDeleteUser() {
        apiSteps.deleteUser(2);
        apiSteps.verifyResponseStatusCode(204);
    }
    
    @Test
    @DisplayName("Register user successfully")
    @Tag("positive")
    @Tag("critical")
    public void testRegisterUserSuccessfully() {
        apiSteps.registerUser("<EMAIL>", "pistol");
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyResponseContainsField("id");
        apiSteps.verifyResponseContainsToken();
    }
    
    @Test
    @DisplayName("Register user without password")
    @Tag("negative")
    public void testRegisterUserWithoutPassword() {
        apiSteps.registerUserWithEmailOnly("sydney@fife");
        apiSteps.verifyResponseStatusCode(400);
        apiSteps.verifyResponseContainsErrorMessage("Missing password");
    }
    
    @Test
    @DisplayName("Login user successfully")
    @Tag("positive")
    @Tag("critical")
    public void testLoginUserSuccessfully() {
        apiSteps.loginUser("<EMAIL>", "cityslicka");
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyResponseContainsToken();
    }
    
    @Test
    @DisplayName("Login user without password")
    @Tag("negative")
    public void testLoginUserWithoutPassword() {
        apiSteps.loginUserWithEmailOnly("peter@klaven");
        apiSteps.verifyResponseStatusCode(400);
        apiSteps.verifyResponseContainsErrorMessage("Missing password");
    }
    
    @ParameterizedTest
    @DisplayName("Test different user registration scenarios")
    @CsvSource({
        "<EMAIL>, pistol, 200",
        "sydney@fife, '', 400"
    })
    @Tag("boundary")
    public void testUserRegistrationScenarios(String email, String password, int expectedStatusCode) {
        if (password.isEmpty()) {
            apiSteps.registerUserWithEmailOnly(email);
        } else {
            apiSteps.registerUser(email, password);
        }
        apiSteps.verifyResponseStatusCode(expectedStatusCode);
    }
    
    @Test
    @DisplayName("Test response time for user retrieval")
    @Tag("performance")
    public void testResponseTimeForUserRetrieval() {
        apiSteps.getUserById(1);
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyResponseTime(2000); // Should respond within 2 seconds
    }
    
    @Test
    @DisplayName("Test delayed response")
    @Tag("performance")
    public void testDelayedResponse() {
        apiSteps.getUserWithDelay(1, 3);
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyUserId(1);
        // Response should take at least 3 seconds due to delay
    }
    
    @Test
    @DisplayName("Verify specific user details")
    @Tag("verification")
    public void testVerifySpecificUserDetails() {
        apiSteps.getUserById(2);
        apiSteps.verifyResponseStatusCode(200);
        apiSteps.verifyUserId(2);
        apiSteps.verifyUserEmail("<EMAIL>");
        apiSteps.verifyUserFirstName("Janet");
        apiSteps.verifyUserLastName("Weaver");
    }
}
