package com.automation.framework;

import com.automation.framework.steps.LoginSteps;
import com.automation.framework.steps.ProductSteps;
import net.serenitybdd.annotations.Managed;
import net.serenitybdd.annotations.Steps;
import net.serenitybdd.junit5.SerenityJUnit5Extension;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.openqa.selenium.WebDriver;

/**
 * JUnit 5 test class for Login functionality using Serenity BDD
 * Demonstrates how to use Serenity with JUnit instead of Cucumber
 */
@ExtendWith(SerenityJUnit5Extension.class)
@DisplayName("Login Tests")
@Tag("login")
public class LoginTest {
    
    @Managed(uniqueSession = true)
    WebDriver driver;
    
    @Steps
    LoginSteps loginSteps;
    
    @Steps
    ProductSteps productSteps;
    
    @Test
    @DisplayName("Successful login with valid credentials")
    @Tag("positive")
    @Tag("critical")
    public void testSuccessfulLogin() {
        loginSteps.navigateToLoginPage();
        loginSteps.verifyLoginPageIsDisplayed();
        loginSteps.loginWithCredentials("standard_user", "secret_sauce");
        loginSteps.verifySuccessfulLogin();
        productSteps.verifyPageTitle("Products");
    }
    
    @Test
    @DisplayName("Login with invalid username")
    @Tag("negative")
    public void testLoginWithInvalidUsername() {
        loginSteps.navigateToLoginPage();
        loginSteps.loginWithCredentials("invalid_user", "secret_sauce");
        loginSteps.verifyLoginErrorMessage("Username and password do not match any user in this service");
    }
    
    @Test
    @DisplayName("Login with invalid password")
    @Tag("negative")
    public void testLoginWithInvalidPassword() {
        loginSteps.navigateToLoginPage();
        loginSteps.loginWithCredentials("standard_user", "invalid_password");
        loginSteps.verifyLoginErrorMessage("Username and password do not match any user in this service");
    }
    
    @Test
    @DisplayName("Login with empty username")
    @Tag("negative")
    public void testLoginWithEmptyUsername() {
        loginSteps.navigateToLoginPage();
        loginSteps.loginWithCredentials("", "secret_sauce");
        loginSteps.verifyLoginErrorMessage("Username is required");
    }
    
    @Test
    @DisplayName("Login with empty password")
    @Tag("negative")
    public void testLoginWithEmptyPassword() {
        loginSteps.navigateToLoginPage();
        loginSteps.loginWithCredentials("standard_user", "");
        loginSteps.verifyLoginErrorMessage("Password is required");
    }
    
    @Test
    @DisplayName("Login with locked out user")
    @Tag("negative")
    public void testLoginWithLockedOutUser() {
        loginSteps.navigateToLoginPage();
        loginSteps.loginWithCredentials("locked_out_user", "secret_sauce");
        loginSteps.verifyLoginErrorMessage("Sorry, this user has been locked out");
    }
    
    @ParameterizedTest
    @DisplayName("Login with different user types")
    @CsvSource({
        "standard_user, secret_sauce, true",
        "problem_user, secret_sauce, true",
        "performance_glitch_user, secret_sauce, true",
        "locked_out_user, secret_sauce, false",
        "invalid_user, secret_sauce, false"
    })
    @Tag("boundary")
    public void testLoginWithDifferentUsers(String username, String password, boolean shouldSucceed) {
        loginSteps.navigateToLoginPage();
        loginSteps.loginWithCredentials(username, password);
        
        if (shouldSucceed) {
            loginSteps.verifySuccessfulLogin();
        } else {
            loginSteps.verifyErrorMessageIsDisplayed();
        }
    }
    
    @Test
    @DisplayName("Verify login page elements")
    @Tag("ui")
    public void testLoginPageElements() {
        loginSteps.navigateToLoginPage();
        loginSteps.verifyLoginPageIsDisplayed();
        loginSteps.verifyPageTitleContains("Swag Labs");
        loginSteps.verifyLoginButtonIsEnabled();
    }
    
    @Test
    @DisplayName("Clear login fields functionality")
    @Tag("ui")
    public void testClearLoginFields() {
        loginSteps.navigateToLoginPage();
        loginSteps.enterUsername("test_user");
        loginSteps.enterPassword("test_password");
        loginSteps.clearAllLoginFields();
        loginSteps.verifyErrorMessageIsNotDisplayed();
    }
    
    @Test
    @DisplayName("Step by step login process")
    @Tag("positive")
    public void testStepByStepLogin() {
        loginSteps.navigateToLoginPage();
        loginSteps.verifyLoginPageIsDisplayed();
        loginSteps.enterUsername("standard_user");
        loginSteps.enterPassword("secret_sauce");
        loginSteps.clickLoginButton();
        loginSteps.verifySuccessfulLogin();
    }
}
