package com.automation.framework;

import com.automation.framework.steps.LoginSteps;
import com.automation.framework.steps.ProductSteps;
import net.serenitybdd.annotations.Managed;
import net.serenitybdd.annotations.Steps;
import net.serenitybdd.junit5.SerenityJUnit5Extension;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.openqa.selenium.WebDriver;

/**
 * JUnit 5 test class for Product functionality using Serenity BDD
 * Demonstrates how to use Serenity with JUnit instead of Cucumber
 */
@ExtendWith(SerenityJUnit5Extension.class)
@DisplayName("Product Tests")
@Tag("products")
public class ProductTest {
    
    @Managed(uniqueSession = true)
    WebDriver driver;
    
    @Steps
    LoginSteps loginSteps;
    
    @Steps
    ProductSteps productSteps;
    
    @BeforeEach
    @DisplayName("Login before each test")
    public void setUp() {
        loginSteps.navigateToLoginPage();
        loginSteps.loginWithCredentials("standard_user", "secret_sauce");
        loginSteps.verifySuccessfulLogin();
    }
    
    @Test
    @DisplayName("View products page")
    @Tag("positive")
    @Tag("critical")
    public void testViewProductsPage() {
        productSteps.verifyProductsPageIsDisplayed();
        productSteps.verifyPageTitle("Products");
        productSteps.verifyNumberOfProducts(6);
    }
    
    @Test
    @DisplayName("Add single product to cart")
    @Tag("positive")
    public void testAddSingleProductToCart() {
        productSteps.addProductToCartByName("Sauce Labs Backpack");
        productSteps.verifyShoppingCartBadgeIsDisplayed();
        productSteps.verifyShoppingCartBadgeCount("1");
    }
    
    @Test
    @DisplayName("Add multiple products to cart")
    @Tag("positive")
    public void testAddMultipleProductsToCart() {
        productSteps.addProductToCartByName("Sauce Labs Backpack");
        productSteps.addProductToCartByName("Sauce Labs Bike Light");
        productSteps.verifyShoppingCartBadgeCount("2");
    }
    
    @Test
    @DisplayName("Add product by index")
    @Tag("positive")
    public void testAddProductByIndex() {
        productSteps.addProductToCartByIndex(0);
        productSteps.verifyShoppingCartBadgeIsDisplayed();
        productSteps.verifyShoppingCartBadgeCount("1");
    }
    
    @Test
    @DisplayName("Sort products by name A to Z")
    @Tag("sorting")
    public void testSortProductsByNameAtoZ() {
        productSteps.selectSortOption("Name (A to Z)");
        productSteps.verifyProductsAreSortedAlphabetically();
    }
    
    @Test
    @DisplayName("Sort products by price low to high")
    @Tag("sorting")
    public void testSortProductsByPriceLowToHigh() {
        productSteps.selectSortOption("Price (low to high)");
        productSteps.verifyProductsAreSortedByPriceLowToHigh();
    }
    
    @Test
    @DisplayName("Sort products by price high to low")
    @Tag("sorting")
    public void testSortProductsByPriceHighToLow() {
        productSteps.selectSortOption("Price (high to low)");
        productSteps.verifyProductsAreSortedByPriceHighToLow();
    }
    
    @Test
    @DisplayName("Verify specific product details")
    @Tag("ui")
    public void testVerifySpecificProductDetails() {
        productSteps.verifyProductIsDisplayed("Sauce Labs Backpack");
        productSteps.verifyProductIsDisplayed("Sauce Labs Bike Light");
        productSteps.verifyProductIsDisplayed("Sauce Labs Bolt T-Shirt");
    }
    
    @Test
    @DisplayName("Navigate to shopping cart")
    @Tag("navigation")
    public void testNavigateToShoppingCart() {
        productSteps.addProductToCartByName("Sauce Labs Backpack");
        productSteps.clickShoppingCart();
        // Note: Would need CartSteps to verify cart page
    }
    
    @Test
    @DisplayName("Verify products page elements")
    @Tag("ui")
    public void testVerifyProductsPageElements() {
        productSteps.verifyPageTitle("Products");
        productSteps.verifyShoppingCartBadgeIsNotDisplayed();
        productSteps.clickMenuButton();
        // Note: Would need MenuSteps to verify menu is displayed
    }
    
    @ParameterizedTest
    @DisplayName("Add different products to cart")
    @ValueSource(strings = {
        "Sauce Labs Backpack",
        "Sauce Labs Bike Light",
        "Sauce Labs Bolt T-Shirt",
        "Sauce Labs Fleece Jacket",
        "Sauce Labs Onesie",
        "Test.allTheThings() T-Shirt (Red)"
    })
    @Tag("data-driven")
    public void testAddDifferentProductsToCart(String productName) {
        productSteps.addProductToCartByName(productName);
        productSteps.verifyShoppingCartBadgeCount("1");
        productSteps.verifyProductIsDisplayed(productName);
    }
    
    @Test
    @DisplayName("Verify product count and names")
    @Tag("verification")
    public void testVerifyProductCountAndNames() {
        productSteps.verifyNumberOfProducts(6);
        
        // Verify first few products exist
        productSteps.verifyProductIsDisplayed("Sauce Labs Backpack");
        productSteps.verifyProductIsDisplayed("Sauce Labs Bike Light");
        productSteps.verifyProductIsDisplayed("Sauce Labs Bolt T-Shirt");
        productSteps.verifyProductIsDisplayed("Sauce Labs Fleece Jacket");
        productSteps.verifyProductIsDisplayed("Sauce Labs Onesie");
        productSteps.verifyProductIsDisplayed("Test.allTheThings() T-Shirt (Red)");
    }
}
