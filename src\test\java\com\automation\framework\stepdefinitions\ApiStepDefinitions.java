package com.automation.framework.stepdefinitions;

import com.automation.framework.steps.ApiSteps;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

/**
 * Cucumber Step Definitions for API functionality
 * Maps Gherkin steps to Java methods for API testing
 */
public class ApiStepDefinitions {
    
    @Steps
    private ApiSteps apiSteps;
    
    @When("I get all users from the API")
    public void i_get_all_users_from_the_api() {
        apiSteps.getAllUsers();
    }
    
    @When("I get users for page {int} from the API")
    public void i_get_users_for_page_from_the_api(int page) {
        apiSteps.getUsersForPage(page);
    }
    
    @When("I get user by ID {int} from the API")
    public void i_get_user_by_id_from_the_api(int userId) {
        apiSteps.getUserById(userId);
    }
    
    @When("I create a user with name {string} and job {string}")
    public void i_create_a_user_with_name_and_job(String name, String job) {
        apiSteps.createUser(name, job);
    }
    
    @When("I update user {int} with name {string} and job {string}")
    public void i_update_user_with_name_and_job(int userId, String name, String job) {
        apiSteps.updateUser(userId, name, job);
    }
    
    @When("I delete user with ID {int}")
    public void i_delete_user_with_id(int userId) {
        apiSteps.deleteUser(userId);
    }
    
    @When("I register user with email {string} and password {string}")
    public void i_register_user_with_email_and_password(String email, String password) {
        apiSteps.registerUser(email, password);
    }
    
    @When("I register user with email only {string}")
    public void i_register_user_with_email_only(String email) {
        apiSteps.registerUserWithEmailOnly(email);
    }
    
    @When("I login user with email {string} and password {string}")
    public void i_login_user_with_email_and_password(String email, String password) {
        apiSteps.loginUser(email, password);
    }
    
    @When("I login user with email only {string}")
    public void i_login_user_with_email_only(String email) {
        apiSteps.loginUserWithEmailOnly(email);
    }
    
    @Then("the API response status code should be {int}")
    public void the_api_response_status_code_should_be(int expectedStatusCode) {
        apiSteps.verifyResponseStatusCode(expectedStatusCode);
    }
    
    @Then("the API response should contain {string} field")
    public void the_api_response_should_contain_field(String fieldName) {
        apiSteps.verifyResponseContainsField(fieldName);
    }
    
    @Then("the API response field {string} should equal {int}")
    public void the_api_response_field_should_equal_int(String fieldName, int expectedValue) {
        apiSteps.verifyResponseFieldEquals(fieldName, expectedValue);
    }
    
    @Then("the API response field {string} should equal {string}")
    public void the_api_response_field_should_equal_string(String fieldName, String expectedValue) {
        apiSteps.verifyResponseFieldEquals(fieldName, expectedValue);
    }
    
    @Then("the API response time should be less than {long} ms")
    public void the_api_response_time_should_be_less_than_ms(long maxTimeInMs) {
        apiSteps.verifyResponseTime(maxTimeInMs);
    }
    
    @Then("the user ID in response should be {int}")
    public void the_user_id_in_response_should_be(int expectedId) {
        apiSteps.verifyUserId(expectedId);
    }
    
    @Then("the user email should be {string}")
    public void the_user_email_should_be(String expectedEmail) {
        apiSteps.verifyUserEmail(expectedEmail);
    }
    
    @Then("the user first name should be {string}")
    public void the_user_first_name_should_be(String expectedFirstName) {
        apiSteps.verifyUserFirstName(expectedFirstName);
    }
    
    @Then("the user last name should be {string}")
    public void the_user_last_name_should_be(String expectedLastName) {
        apiSteps.verifyUserLastName(expectedLastName);
    }
    
    @Then("the created user name should be {string}")
    public void the_created_user_name_should_be(String expectedName) {
        apiSteps.verifyCreatedUserName(expectedName);
    }
    
    @Then("the created user job should be {string}")
    public void the_created_user_job_should_be(String expectedJob) {
        apiSteps.verifyCreatedUserJob(expectedJob);
    }
    
    @Then("the API response should contain token")
    public void the_api_response_should_contain_token() {
        apiSteps.verifyResponseContainsToken();
    }
    
    @Then("the API response should contain error message {string}")
    public void the_api_response_should_contain_error_message(String expectedError) {
        apiSteps.verifyResponseContainsErrorMessage(expectedError);
    }
}
