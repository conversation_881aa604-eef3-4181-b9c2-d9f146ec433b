package com.automation.framework.stepdefinitions;

import com.automation.framework.steps.LoginSteps;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

/**
 * Cucumber Step Definitions for Login functionality
 * Maps Gherkin steps to Java methods
 */
public class LoginStepDefinitions {
    
    @Steps
    private LoginSteps loginSteps;
    
    @Given("I am on the login page")
    public void i_am_on_the_login_page() {
        loginSteps.navigateToLoginPage();
        loginSteps.verifyLoginPageIsDisplayed();
    }
    
    @Given("I navigate to the login page")
    public void i_navigate_to_the_login_page() {
        loginSteps.navigateToLoginPage();
    }
    
    @When("I enter username {string}")
    public void i_enter_username(String username) {
        loginSteps.enterUsername(username);
    }
    
    @When("I enter password {string}")
    public void i_enter_password(String password) {
        loginSteps.enterPassword(password);
    }
    
    @When("I click the login button")
    public void i_click_the_login_button() {
        loginSteps.clickLoginButton();
    }
    
    @When("I login with username {string} and password {string}")
    public void i_login_with_username_and_password(String username, String password) {
        loginSteps.loginWithCredentials(username, password);
    }
    
    @When("I clear the username field")
    public void i_clear_the_username_field() {
        loginSteps.clearUsernameField();
    }
    
    @When("I clear the password field")
    public void i_clear_the_password_field() {
        loginSteps.clearPasswordField();
    }
    
    @When("I clear all login fields")
    public void i_clear_all_login_fields() {
        loginSteps.clearAllLoginFields();
    }
    
    @Then("I should be successfully logged in")
    public void i_should_be_successfully_logged_in() {
        loginSteps.verifySuccessfulLogin();
    }
    
    @Then("I should see the products page")
    public void i_should_see_the_products_page() {
        loginSteps.verifySuccessfulLogin();
    }
    
    @Then("I should see an error message {string}")
    public void i_should_see_an_error_message(String expectedMessage) {
        loginSteps.verifyLoginErrorMessage(expectedMessage);
    }
    
    @Then("I should see an error message")
    public void i_should_see_an_error_message() {
        loginSteps.verifyErrorMessageIsDisplayed();
    }
    
    @Then("I should not see an error message")
    public void i_should_not_see_an_error_message() {
        loginSteps.verifyErrorMessageIsNotDisplayed();
    }
    
    @Then("the login page should be displayed")
    public void the_login_page_should_be_displayed() {
        loginSteps.verifyLoginPageIsDisplayed();
    }
    
    @Then("the login button should be enabled")
    public void the_login_button_should_be_enabled() {
        loginSteps.verifyLoginButtonIsEnabled();
    }
    
    @Then("the login button should be disabled")
    public void the_login_button_should_be_disabled() {
        loginSteps.verifyLoginButtonIsDisabled();
    }
    
    @Then("the page title should contain {string}")
    public void the_page_title_should_contain(String expectedTitle) {
        loginSteps.verifyPageTitleContains(expectedTitle);
    }
    
    @Then("the username field placeholder should be {string}")
    public void the_username_field_placeholder_should_be(String expectedPlaceholder) {
        loginSteps.verifyUsernameFieldPlaceholder(expectedPlaceholder);
    }
    
    @Then("the password field placeholder should be {string}")
    public void the_password_field_placeholder_should_be(String expectedPlaceholder) {
        loginSteps.verifyPasswordFieldPlaceholder(expectedPlaceholder);
    }
}
