package com.automation.framework.stepdefinitions;

import com.automation.framework.steps.ProductSteps;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import net.serenitybdd.annotations.Steps;

/**
 * Cucumber Step Definitions for Product functionality
 * Maps Gherkin steps to Java methods
 */
public class ProductStepDefinitions {
    
    @Steps
    private ProductSteps productSteps;
    
    @Then("I should see the products page")
    public void i_should_see_the_products_page() {
        productSteps.verifyProductsPageIsDisplayed();
    }
    
    @Then("the page title should be {string}")
    public void the_page_title_should_be(String expectedTitle) {
        productSteps.verifyPageTitle(expectedTitle);
    }
    
    @Then("I should see {int} products")
    public void i_should_see_products(int expectedCount) {
        productSteps.verifyNumberOfProducts(expectedCount);
    }
    
    @When("I add the product at index {int} to cart")
    public void i_add_the_product_at_index_to_cart(int index) {
        productSteps.addProductToCartByIndex(index);
    }
    
    @When("I add the product {string} to cart")
    public void i_add_the_product_to_cart(String productName) {
        productSteps.addProductToCartByName(productName);
    }
    
    @When("I click the shopping cart")
    public void i_click_the_shopping_cart() {
        productSteps.clickShoppingCart();
    }
    
    @When("I click the menu button")
    public void i_click_the_menu_button() {
        productSteps.clickMenuButton();
    }
    
    @When("I select sort option {string}")
    public void i_select_sort_option(String sortOption) {
        productSteps.selectSortOption(sortOption);
    }
    
    @Then("the shopping cart badge should show {string}")
    public void the_shopping_cart_badge_should_show(String expectedCount) {
        productSteps.verifyShoppingCartBadgeCount(expectedCount);
    }
    
    @Then("the shopping cart badge should be displayed")
    public void the_shopping_cart_badge_should_be_displayed() {
        productSteps.verifyShoppingCartBadgeIsDisplayed();
    }
    
    @Then("the shopping cart badge should not be displayed")
    public void the_shopping_cart_badge_should_not_be_displayed() {
        productSteps.verifyShoppingCartBadgeIsNotDisplayed();
    }
    
    @Then("the product at index {int} should be {string}")
    public void the_product_at_index_should_be(int index, String expectedName) {
        productSteps.verifyProductNameAtIndex(index, expectedName);
    }
    
    @Then("the price at index {int} should be {string}")
    public void the_price_at_index_should_be(int index, String expectedPrice) {
        productSteps.verifyProductPriceAtIndex(index, expectedPrice);
    }
    
    @Then("the product {string} should be displayed")
    public void the_product_should_be_displayed(String productName) {
        productSteps.verifyProductIsDisplayed(productName);
    }
    
    @Then("the product {string} should not be displayed")
    public void the_product_should_not_be_displayed(String productName) {
        productSteps.verifyProductIsNotDisplayed(productName);
    }
    
    @Then("the products should be sorted alphabetically")
    public void the_products_should_be_sorted_alphabetically() {
        productSteps.verifyProductsAreSortedAlphabetically();
    }
    
    @Then("the products should be sorted by price low to high")
    public void the_products_should_be_sorted_by_price_low_to_high() {
        productSteps.verifyProductsAreSortedByPriceLowToHigh();
    }
    
    @Then("the products should be sorted by price high to low")
    public void the_products_should_be_sorted_by_price_high_to_low() {
        productSteps.verifyProductsAreSortedByPriceHighToLow();
    }
}
