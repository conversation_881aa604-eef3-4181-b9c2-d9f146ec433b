package runners;

import io.cucumber.junit.platform.engine.Constants;
import org.junit.platform.suite.api.ConfigurationParameter;
import org.junit.platform.suite.api.IncludeEngines;
import org.junit.platform.suite.api.SelectClasspathResource;
import org.junit.platform.suite.api.Suite;

/**
 * Smoke Test Runner for running only smoke tests
 * Filters tests by @smoke tag
 */
@Suite
@IncludeEngines("cucumber")
@SelectClasspathResource("features")
@ConfigurationParameter(key = Constants.PLUGIN_PROPERTY_NAME, 
    value = "pretty, html:target/cucumber-reports/smoke, json:target/cucumber-reports/smoke/Cucumber.json")
@ConfigurationParameter(key = Constants.GLUE_PROPERTY_NAME, 
    value = "com.automation.framework.stepdefinitions")
@ConfigurationParameter(key = Constants.FILTER_TAGS_PROPERTY_NAME, 
    value = "@smoke and not @ignore")
public class SmokeTestRunner {
    // This class runs only smoke tests tagged with @smoke
}
