# Environment Configuration File
# This file contains environment-specific configurations

# Default Environment
default.environment=local

# Local Environment Configuration
local.base.url=https://www.saucedemo.com
local.api.url=https://reqres.in/api
local.browser=chrome
local.headless=false
local.timeout=30

# Development Environment Configuration
dev.base.url=https://dev.saucedemo.com
dev.api.url=https://dev-api.example.com
dev.browser=chrome
dev.headless=false
dev.timeout=30

# Staging Environment Configuration
staging.base.url=https://staging.saucedemo.com
staging.api.url=https://staging-api.example.com
staging.browser=chrome
staging.headless=true
staging.timeout=45

# Production Environment Configuration
prod.base.url=https://www.saucedemo.com
prod.api.url=https://api.example.com
prod.browser=chrome
prod.headless=true
prod.timeout=60

# Test Users Configuration
test.user.standard=standard_user
test.user.locked=locked_out_user
test.user.problem=problem_user
test.user.performance=performance_glitch_user
test.password=secret_sauce

# Database Configuration (if needed)
db.local.url=**********************************
db.local.username=testuser
db.local.password=testpass

db.staging.url=***********************************
db.staging.username=staginguser
db.staging.password=stagingpass
