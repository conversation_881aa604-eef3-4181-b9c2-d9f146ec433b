@api @smoke
Feature: API Testing
  As a developer
  I want to test the API endpoints
  So that I can ensure the API functionality works correctly

  @positive @critical
  Scenario: Get all users successfully
    When I get all users from the API
    Then the API response status code should be 200
    And the API response should contain "data" field
    And the API response time should be less than 5000 ms

  @positive
  Scenario: Get users with pagination
    When I get users for page 2 from the API
    Then the API response status code should be 200
    And the API response field "page" should equal 2
    And the API response should contain "data" field
    And the API response should contain "total" field

  @positive
  Scenario Outline: Get user by different IDs
    When I get user by ID <user_id> from the API
    Then the API response status code should be 200
    And the user ID in response should be <user_id>
    And the API response should contain "data.email" field
    And the API response should contain "data.first_name" field
    And the API response should contain "data.last_name" field

    Examples:
      | user_id |
      | 1       |
      | 2       |
      | 3       |

  @negative
  Scenario: Get non-existent user
    When I get user by ID 999 from the API
    Then the API response status code should be 404

  @positive @critical
  Scenario: Create new user successfully
    When I create a user with name "<PERSON>" and job "Software Engineer"
    Then the API response status code should be 201
    And the created user name should be "<PERSON>"
    And the created user job should be "Software Engineer"
    And the API response should contain "id" field
    And the API response should contain "createdAt" field

  @positive
  Scenario: Update existing user
    When I update user 2 with name "Updated Name" and job "Updated Job"
    Then the API response status code should be 200
    And the created user name should be "Updated Name"
    And the created user job should be "Updated Job"
    And the API response should contain "updatedAt" field

  @positive
  Scenario: Delete user successfully
    When I delete user with ID 2
    Then the API response status code should be 204

  @positive @critical
  Scenario: Register user successfully
    When I register user with email "<EMAIL>" and password "pistol"
    Then the API response status code should be 200
    And the API response should contain "id" field
    And the API response should contain token

  @negative
  Scenario: Register user without password
    When I register user with email only "sydney@fife"
    Then the API response status code should be 400
    And the API response should contain error message "Missing password"

  @positive @critical
  Scenario: Login user successfully
    When I login user with email "<EMAIL>" and password "cityslicka"
    Then the API response status code should be 200
    And the API response should contain token

  @negative
  Scenario: Login user without password
    When I login user with email only "peter@klaven"
    Then the API response status code should be 400
    And the API response should contain error message "Missing password"

  @performance
  Scenario: Test API response time
    When I get user by ID 1 from the API
    Then the API response status code should be 200
    And the API response time should be less than 2000 ms

  @verification
  Scenario: Verify specific user details
    When I get user by ID 2 from the API
    Then the API response status code should be 200
    And the user ID in response should be 2
    And the user email should be "<EMAIL>"
    And the user first name should be "Janet"
    And the user last name should be "Weaver"
