@login @smoke
Feature: User Login
  As a user
  I want to be able to login to the application
  So that I can access the products page

  Background:
    Given I am on the login page

  @positive @critical
  Scenario: Successful login with valid credentials
    When I login with username "standard_user" and password "secret_sauce"
    Then I should be successfully logged in
    And the page title should be "Products"

  @positive
  Scenario: Successful login with step by step approach
    When I enter username "standard_user"
    And I enter password "secret_sauce"
    And I click the login button
    Then I should see the products page

  @negative
  Scenario: Login with invalid username
    When I login with username "invalid_user" and password "secret_sauce"
    Then I should see an error message "Username and password do not match any user in this service"

  @negative
  Scenario: Login with invalid password
    When I login with username "standard_user" and password "invalid_password"
    Then I should see an error message "Username and password do not match any user in this service"

  @negative
  Scenario: Login with empty username
    When I login with username "" and password "secret_sauce"
    Then I should see an error message "Username is required"

  @negative
  Scenario: Login with empty password
    When I login with username "standard_user" and password ""
    Then I should see an error message "Password is required"

  @negative
  Scenario: Login with empty credentials
    When I login with username "" and password ""
    Then I should see an error message "Username is required"

  @negative
  Scenario: Login with locked out user
    When I login with username "locked_out_user" and password "secret_sauce"
    Then I should see an error message "Sorry, this user has been locked out"

  @boundary
  Scenario Outline: Login with different user types
    When I login with username "<username>" and password "<password>"
    Then I should see <result>

    Examples:
      | username                | password     | result                                                                    |
      | standard_user           | secret_sauce | the products page                                                         |
      | problem_user            | secret_sauce | the products page                                                         |
      | performance_glitch_user | secret_sauce | the products page                                                         |
      | locked_out_user         | secret_sauce | an error message "Sorry, this user has been locked out"                  |
      | invalid_user            | secret_sauce | an error message "Username and password do not match any user in this service" |

  @ui
  Scenario: Verify login page elements
    Then the login page should be displayed
    And the page title should contain "Swag Labs"
    And the login button should be enabled

  @ui
  Scenario: Clear login fields
    When I enter username "test_user"
    And I enter password "test_password"
    And I clear all login fields
    Then I should not see an error message
