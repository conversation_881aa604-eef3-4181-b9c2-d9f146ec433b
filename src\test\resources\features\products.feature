@products @smoke
Feature: Product Management
  As a logged-in user
  I want to be able to view and manage products
  So that I can add items to my shopping cart

  Background:
    Given I am on the login page
    When I login with username "standard_user" and password "secret_sauce"
    Then I should see the products page

  @positive @critical
  Scenario: View products page
    Then the page title should be "Products"
    And I should see 6 products

  @positive
  Scenario: Add single product to cart
    When I add the product "Sauce Labs Backpack" to cart
    Then the shopping cart badge should be displayed
    And the shopping cart badge should show "1"

  @positive
  Scenario: Add multiple products to cart
    When I add the product "Sauce Labs Backpack" to cart
    And I add the product "Sauce Labs Bike Light" to cart
    Then the shopping cart badge should show "2"

  @positive
  Scenario: Add product by index
    When I add the product at index 0 to cart
    Then the shopping cart badge should be displayed
    And the shopping cart badge should show "1"

  @sorting
  Scenario: Sort products by name A to Z
    When I select sort option "Name (A to Z)"
    Then the products should be sorted alphabetically

  @sorting
  Scenario: Sort products by price low to high
    When I select sort option "Price (low to high)"
    Then the products should be sorted by price low to high

  @sorting
  Scenario: Sort products by price high to low
    When I select sort option "Price (high to low)"
    Then the products should be sorted by price high to low

  @ui
  Scenario: Verify specific product details
    Then the product "Sauce Labs Backpack" should be displayed
    And the product "Sauce Labs Bike Light" should be displayed
    And the product "Sauce Labs Bolt T-Shirt" should be displayed

  @navigation
  Scenario: Navigate to shopping cart
    When I add the product "Sauce Labs Backpack" to cart
    And I click the shopping cart
    Then I should see the shopping cart page

  @ui
  Scenario: Verify products page elements
    Then the page title should be "Products"
    And the shopping cart badge should not be displayed
    When I click the menu button
    Then the menu should be displayed

  @data-driven
  Scenario Outline: Add different products to cart
    When I add the product "<product_name>" to cart
    Then the shopping cart badge should show "1"
    And the product "<product_name>" should be displayed

    Examples:
      | product_name                  |
      | Sauce Labs Backpack           |
      | Sauce Labs Bike Light         |
      | Sauce Labs Bolt T-Shirt       |
      | Sauce Labs Fleece Jacket      |
      | Sauce Labs Onesie             |
      | Test.allTheThings() T-Shirt (Red) |
