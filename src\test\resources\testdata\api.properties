# API Test Data
# Contains API endpoints and test data for API testing

# Base URLs
api.base.url=https://reqres.in/api
api.users.endpoint=/users
api.register.endpoint=/register
api.login.endpoint=/login

# Test User Data for API
api.create.user.name=morpheus
api.create.user.job=leader

api.update.user.name=morpheus
api.update.user.job=zion resident

# Registration Data
api.register.email=<EMAIL>
api.register.password=pistol

api.register.invalid.email=sydney@fife
api.register.missing.password=

# Login Data
api.login.email=<EMAIL>
api.login.password=cityslicka

api.login.invalid.email=peter@klaven
api.login.missing.password=

# Expected Response Data
api.user1.id=1
api.user1.email=<EMAIL>
api.user1.first_name=George
api.user1.last_name=Bluth

api.user2.id=2
api.user2.email=<EMAIL>
api.user2.first_name=Janet
api.user2.last_name=Weaver

# Error Messages
api.error.missing.password=Missing password
api.error.user.not.found=Not Found

# Response Validation
api.response.timeout=5000
api.response.max_time=3000

# Pagination
api.page.default=1
api.page.size=6
api.total.users=12
api.total.pages=2

# HTTP Status Codes
status.ok=200
status.created=201
status.no_content=204
status.bad_request=400
status.not_found=404
status.internal_server_error=500
