# User Test Data
# Contains user credentials and related test data

# Standard User
standard.username=standard_user
standard.password=secret_sauce

# Locked Out User
locked.username=locked_out_user
locked.password=secret_sauce

# Problem User
problem.username=problem_user
problem.password=secret_sauce

# Performance Glitch User
performance.username=performance_glitch_user
performance.password=secret_sauce

# Invalid User Credentials
invalid.username=invalid_user
invalid.password=invalid_password

# Empty Credentials
empty.username=
empty.password=

# API Test Users
api.user1.email=<EMAIL>
api.user1.password=pistol

api.user2.email=<EMAIL>
api.user2.password=cityslicka

api.user3.email=sydney@fife
api.user3.password=

# Test User Data for Registration
test.user.name=John Doe
test.user.job=Software Engineer
test.user.email=<EMAIL>

# User Profile Data
profile.first_name=Test
profile.last_name=User
profile.phone=+1234567890
profile.address=123 Test Street
profile.city=Test City
profile.state=Test State
profile.zip=12345
profile.country=Test Country
