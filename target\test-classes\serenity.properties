# Serenity BDD Configuration File
# This file contains configuration settings for Serenity BDD test execution and reporting

# WebDriver Configuration
webdriver.driver=chrome
webdriver.chrome.driver=
webdriver.autodownload=true
webdriver.timeouts.implicitlywait=10000
webdriver.wait.for.timeout=30000

# Browser Configuration
chrome.switches=--disable-extensions,--disable-plugins,--disable-images,--disable-web-security,--allow-running-insecure-content,--ignore-certificate-errors,--disable-popup-blocking,--disable-default-apps,--disable-extensions-file-access-check,--disable-infobars,--remote-allow-origins=*
chrome.preferences=profile.default_content_setting_values.notifications:2

# Headless mode (set to true for CI/CD environments)
headless.mode=false

# Base URL for the application under test
webdriver.base.url=https://www.saucedemo.com

# Screenshot Configuration
serenity.take.screenshots=FOR_FAILURES
serenity.screenshot.strategy=AFTER_EACH_STEP
serenity.capture.screenshot.on.failure=true

# Reporting Configuration
serenity.project.name=Automation Framework
serenity.test.root=com.automation.framework
serenity.outputDirectory=target/site/serenity
serenity.reports.show.step.details=true
serenity.report.show.manual.tests=true
serenity.report.show.releases=true

# Logging Configuration
serenity.logging=VERBOSE
serenity.verbose.steps=false
serenity.log.level=INFO

# Parallel Execution
serenity.batch.strategy=DIVIDE_EQUALLY
serenity.batch.count=2

# Restart browser configuration
serenity.restart.browser.for.each=example
serenity.browser.maximized=true

# Cucumber Configuration
cucumber.options=--plugin pretty --plugin html:target/cucumber-reports --plugin json:target/cucumber-reports/Cucumber.json --plugin junit:target/cucumber-reports/Cucumber.xml

# Environment specific configurations
environments.default=local
environments.all=local,dev,staging,prod

# Local Environment
environments.local.webdriver.base.url=https://www.saucedemo.com
environments.local.api.base.url=https://reqres.in/api

# Dev Environment
environments.dev.webdriver.base.url=https://dev.saucedemo.com
environments.dev.api.base.url=https://dev-api.example.com

# Staging Environment
environments.staging.webdriver.base.url=https://staging.saucedemo.com
environments.staging.api.base.url=https://staging-api.example.com

# Production Environment
environments.prod.webdriver.base.url=https://www.saucedemo.com
environments.prod.api.base.url=https://api.example.com

# API Testing Configuration
api.base.url=https://reqres.in/api
api.timeout=30000

# Test Data Configuration
test.data.directory=src/test/resources/testdata

# Custom Properties
custom.retry.count=2
custom.page.load.timeout=30
custom.element.timeout=10
